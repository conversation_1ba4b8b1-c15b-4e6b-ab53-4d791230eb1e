#!/usr/bin/env python
# encoding: utf-8
"""
  @file   main.py
  <AUTHOR>
  @brief
    This application provides the API interface into the Druid instances.

  @copyright Dense Air Limited Copyright (c) 2024

"""

import argparse
import asyncio
import importlib.resources
import logging
import pathlib
import sys
from collections import deque

import druid_interactions.migrations
import hypercorn
from da_common.config import Config
from da_common.health_check import health_router
from da_common.middleware import NMSMiddleware
from da_common.security import security_router
from druid_interactions.api import alarms_router
from druid_interactions.api import enodebs_router
from druid_interactions.api import features_router
from druid_interactions.api import instance_router
from druid_interactions.api import reports_router
from druid_interactions.api import system_router
from druid_interactions.migrations.main import process_migrations
from druid_interactions.migrations.utils import add_migration_arguments
from druid_interactions.migrations.utils import assemble_migrations
from druid_interactions.monitor import Monitor
from druid_interactions.pool import PoolFactory
from druid_interactions.reporter import Reporter
from druid_interactions.watchdog import Watchdog
from fastapi import FastAPI
from hypercorn.asyncio import serve


def build_description(changelog="CHANGELOG.md"):
    changes = importlib.resources.files("druid_interactions").joinpath(changelog).read_text()
    lines = deque(changes.splitlines(keepends=False), maxlen=24)
    return "\n".join(
        ("Druid Interactions is a broker for operations on Druid instances.", "", *lines)
    )


def build_app(config, **kwargs):
    """Assemble a FastAPI app"""

    app = FastAPI(
        title="Druid Interactions",
        version=druid_interactions.__version__,
        description=build_description(),
        **kwargs,
    )
    app.state.clients = {}
    app.state.config = config
    app.state.package_name = druid_interactions.__name__
    app.add_middleware(NMSMiddleware)
    app.include_router(health_router)
    app.include_router(alarms_router)
    app.include_router(enodebs_router)
    app.include_router(security_router)
    app.include_router(features_router)
    app.include_router(instance_router)
    app.include_router(reports_router)
    app.include_router(system_router)

    return app


def main(args):
    logger = logging.getLogger("druid_interactions")

    logger.info(f"Druid Interactions application version {druid_interactions.__version__}")

    if args.version:
        print(druid_interactions.__version__, file=sys.stdout)
        return 0

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    config = loop.run_until_complete(Config.get_config(args.config))
    pool = loop.run_until_complete(PoolFactory.create(config))

    if args.list or args.read or args.run:
        actions = assemble_migrations(druid_interactions.migrations)
        loop.run_until_complete(
            process_migrations(args=args, config=config, actions=actions, pool=pool)
        )

    app = build_app(config)
    app.state.pool = pool
    logger.info(
        f"Database pool using {config.__class__.__name__}"
        f" and {app.state.pool.backend.__name__} backend"
    )

    app.state.clients = config.build_clients()
    app.state.reporter = Reporter(config, pool)
    app.state.watchdog = Watchdog(config, pool)
    app.state.monitor = Monitor(config, pool)
    app.state.tasks = [
        loop.create_task(app.state.reporter.run()),
        loop.create_task(app.state.watchdog.run()),
        loop.create_task(app.state.monitor.run()),
    ]

    logger.info(f"Services: {config.services}")
    for name, obj in app.state.clients.items():
        if obj is None:
            logger.warning(f"Unable to create client for service '{name}'")

    settings = hypercorn.Config.from_mapping(config.data.get("app", {}))
    loop.run_until_complete(serve(app, settings))
    return 0


def parser(default_config=""):
    rv = argparse.ArgumentParser(usage=__doc__)
    application_group = rv.add_argument_group("application")
    migration_group = rv.add_argument_group("migration")

    application_group.add_argument(
        "--config",
        required=False,
        type=pathlib.Path,
        default=default_config,
        help=f"Set the path to a TOML configuration file [{default_config}].",
    )
    application_group.add_argument(
        "--version", action="store_true", default=False, help="Display package version"
    )
    add_migration_arguments(migration_group)
    return rv


def run():
    config = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
    p = parser(default_config=config)
    args = p.parse_args()
    rv = main(args)
    sys.exit(rv)


if __name__ == "__main__":
    run()
