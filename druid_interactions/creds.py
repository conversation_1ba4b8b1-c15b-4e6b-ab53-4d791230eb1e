import warnings

from da_common.config import Config
from da_common.secrets import get_secret_data
from google.api_core.exceptions import GoogleAPIError


async def get_credentials(config: Config, secret_id: str = None) -> dict:
    project_id = config.data.get("google", {}).get("project", "")
    try:
        return await get_secret_data(project_id=project_id, secret_id=secret_id)
    except GoogleAPIError as e:
        warnings.warn(f"Unable to access credentials: {e}")
        return {}
