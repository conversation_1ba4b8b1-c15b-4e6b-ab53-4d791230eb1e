import asyncio
import datetime
import logging
import sys
from collections import Counter
from collections import defaultdict
from typing import Optional
from uuid import uuid4

from da_common.config import Config
from dal_pubsub.pubsub import PubSub
from druid_interactions.creds import get_credentials
from druid_interactions.models.instance import Instance
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.aspects import AspectsReport
from druid_interactions.queries.entities import EntitiesReport
from druid_interactions.queries.features import LicenceReport
from druid_interactions.queries.networks import NetworksReport
from druid_interactions.queries.segw import SeGWReport
from druid_interactions.queries.sessions import SessionsReport
from metrics_collector.api_schema.models import AlarmStatus
from metrics_collector.api_schema.models import EventData
from metrics_collector.api_schema.models import EventHeader
from metrics_collector.api_schema.models import MetricsEvent


class Reporter:
    def __init__(
        self,
        config: Config,
        pool=None,
        interval: int = 30,
    ):
        self.config = config
        self.pool = pool
        self.logger = logging.getLogger("druid_interactions.reporter")
        self.interval = interval
        self.logger.info(f"Interval configured to {self.interval}s")
        self.clients = {}
        self.reports = defaultdict(dict)
        self.pubsub = None

    def setup_pubsub(self, loop: Optional[asyncio.AbstractEventLoop] = None) -> bool:
        """
        Set up the PubSub connection if it is not already established.

        Args:
            loop: Optional asyncio event loop to use for setting up the connection.

        Returns:
            True if PubSub connection is successfully established, False otherwise.
        """
        if self.pubsub is not None:
            return True

        try:
            self.pubsub = PubSub(config=self.config, loop=loop)
            self.pubsub.set_topic("nms-metrics")
            self.logger.info("PubSub: [ nms-metrics ] connected.")
            return True
        except Exception as e:
            self.logger.warning(f"Unable to connect to PubSub: [ nms-metrics ] service: {e}")
            return False

    def create_license_metrics(self, instance_name: str) -> dict:
        """
        Create a dictionary of license metrics.
        """

        instance_report = self.reports[instance_name]
        aspects = instance_report.get(AspectsReport, {})
        details = instance_report.get(LicenceReport, {})
        networks = instance_report.get(NetworksReport, [])
        segw = instance_report.get(SeGWReport, {})
        enodebs = instance_report.get(EntitiesReport, [])

        is4GCell = not segw.get("s1client", [])
        usedCellLicenses = len(
            [enodeb for enodeb in enodebs if enodeb.get("oper_state") == "enabled"]
        )

        if is4GCell:
            # configured cells , connected , disconnected, locked and unlocked details are calculated on enodebtrx
            enodeb_trxs = []
            for enodeb in enodebs:
                enodeb_trxs.extend(enodeb.get("enb_trx"))
            oper_ctr = Counter(
                enodeb_trx.get("oper_state", "unknown") for enodeb_trx in enodeb_trxs
            )
            admin_ctr = Counter(
                enodeb_trx.get("admin_state", "unknown") for enodeb_trx in enodeb_trxs
            )
        else:
            oper_ctr = Counter(enodeb.get("oper_state", "unknown") for enodeb in enodebs)
            admin_ctr = Counter(enodeb.get("admin_state", "unknown") for enodeb in enodebs)

        return {
            "licenses": {
                "users": {
                    "used": int(aspects.get("users_count", 0)),
                    "total": int(
                        details.get("features", {}).get("enbgw_max_active_subs", 0)
                        or details.get("features", {}).get("max_nbr_of_subs", 0)
                    ),
                },
                "ipsec": {
                    "used": len(networks),
                    "total": int(details.get("features", {}).get("max_pdns", 0)),
                },
                "cells": {
                    "used": usedCellLicenses,
                    "total": int(
                        details.get("features", {}).get("max_enbs", 0)
                        or details.get("features", {}).get("enbgw_max_enbs", 0)
                    ),
                },
            },
            "enodebs": {
                "configured": len(enodebs),
                "enabled": oper_ctr["enabled"],
                "disabled": oper_ctr["disabled"],
                "locked": admin_ctr["locked"],
                "unlocked": admin_ctr["unlocked"],
            },
        }

    def publish_license_metrics(self, instance: Instance) -> None:
        license_metrics = self.create_license_metrics(instance.name)
        current_time = datetime.datetime.now(datetime.timezone.utc).isoformat()
        event_header = EventHeader(
            domain="nms",
            eventId=uuid4().hex,
            eventName="License Metrics",
            eventType="metric",
            priority="low",
            reportingEntityName="druid_interactions",
            sourceName=instance.name,
            sourceId=instance.name,
            eventTime=current_time,
            eventDuration=0,
            systemDN="druid-interactions",
        )
        event_data = EventData(
            objectId=instance.name,
            objectType="DRUID",
            streetCellId="",
            uri=instance.host,
            type="License Metrics",
            cause="",
            perceivedSeverity="minor",
            specificProblem="",
            trendIndication=AlarmStatus.new.value,
            monitoredAttributes=[license_metrics],
            proposedRepairActions=[],
            additionalText="",
            additionalInformation="",
        )
        payload = MetricsEvent(
            header=event_header,
            data=event_data,
        )
        self.pubsub.push_payload(payload.model_dump())
        self.logger.info(
            f"Druid: [ {instance.name} ], License metrics published with payload: {payload.model_dump()}"
        )

    async def run(self, limit=sys.maxsize, loop=None):
        self.logger.info("Reporter coroutine running")
        self.pool = self.pool or await PoolFactory.create(self.config)
        self.logger.info(f"Using pool {self.pool}")
        self.setup_pubsub(loop)
        while limit:
            if limit % 5 == 0:
                self.logger.info("Creating reports")

            async with self.pool.acquire() as connection:
                async with connection.transaction():
                    sql, args = self.pool.render(Instance(None, None).sql_select())
                    self.logger.info(f"Query: {sql=} {args=}")
                    rows = await connection.fetch(sql, *args)
                    instances = [Instance(**dict(row)) for row in rows]

                    for instance in instances:
                        self.logger.info(f"Accessing {instance.name}...")
                        creds = await get_credentials(self.config, secret_id=instance.secret_id)
                        for report in [
                            AspectsReport(),
                            EntitiesReport(),
                            LicenceReport(),
                            NetworksReport(),
                            SeGWReport(),
                            SessionsReport(),
                        ]:
                            try:
                                key = report.__class__
                                data = await report.get_report_data(
                                    instance, clients=self.clients, creds=creds, verify=False
                                )
                                self.logger.info(f"{key}: {len(data)} item(s)")
                                self.reports[instance.name][key] = report.build_report(data)
                                await asyncio.sleep(0.5)  # <Response [429 Too Many Requests]>

                            except Exception as e:
                                self.logger.warning(f"{e!s}")
                                continue

                        self.publish_license_metrics(instance)

            limit = limit - 1
            await asyncio.sleep(self.interval)
