from collections.abc import Generator
from typing import <PERSON>ple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    """
    Create indexes and add constraint for alarm_id.

    This migration:
    1. Adds indexes on device_id, alarm_type, and alarm_id
    2. Adds a composite index
    3. Creates a sequence for alarm_id
    """
    # Just create indexes - these are safe operations that won't affect existing data
    # or structure, and don't depend on column renames

    # First index
    yield (
        """
        CREATE INDEX IF NOT EXISTS idx_alarms_device_id
        ON druid_interactions.alarms (device_id);
        """,
        {},
    )

    # Second index
    yield (
        """
        CREATE INDEX IF NOT EXISTS idx_alarms_alarm_type
        ON druid_interactions.alarms (alarm_type);
        """,
        {},
    )

    # Third index (on druid_alarm_id - note we're NOT renaming the column)
    yield (
        """
        CREATE INDEX IF NOT EXISTS idx_alarms_druid_alarm_id
        ON druid_interactions.alarms (druid_alarm_id);
        """,
        {},
    )

    # Composite index
    yield (
        """
        CREATE INDEX IF NOT EXISTS idx_alarms_device_alarm
        ON druid_interactions.alarms (device_id, alarm_type);
        """,
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    """
    Revert the changes by dropping the indexes and sequence.
    """
    # Drop indexes
    yield (
        """
        DROP INDEX IF EXISTS idx_alarms_device_id;
        """,
        {},
    )

    yield (
        """
        DROP INDEX IF EXISTS idx_alarms_alarm_type;
        """,
        {},
    )

    yield (
        """
        DROP INDEX IF EXISTS idx_alarms_druid_alarm_id;
        """,
        {},
    )

    yield (
        """
        DROP INDEX IF EXISTS idx_alarms_device_alarm;
        """,
        {},
    )

    # Drop sequence
    yield (
        """
        DROP SEQUENCE IF EXISTS alarm_id_seq;
        """,
        {},
    )
