import textwrap
from collections.abc import Generator
from typing import Tuple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Add status column to druid_interactions.instances table."
    yield (
        textwrap.dedent(
            """
        ALTER TABLE druid_interactions.instances ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'UNKNOWN';
        """
        ),
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Removes the status column from druid_interactions.instances table."
    yield ("ALTER TABLE druid_interactions.instances DROP COLUMN IF EXISTS status", {})
