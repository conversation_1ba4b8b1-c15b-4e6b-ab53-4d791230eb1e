import textwrap
from collections.abc import Generator
from typing import Tuple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Creates the first version of the druid_interactions.instances table."
    yield (
        textwrap.dedent(
            """
        CREATE TABLE IF NOT EXISTS druid_interactions.instances (
            name        TEXT,
            host        TEXT,
            port        INT,
            role        TEXT,
            secret_id   TEXT,
            created_at  timestamptz,
            PRIMARY KEY (name)
        )
        """
        ),
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Removes the druid_interactions.instances table."
    yield ("DROP TABLE IF EXISTS druid_interactions.instances", {})
