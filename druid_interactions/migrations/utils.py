import argparse
import importlib.resources
import importlib.util
import logging
import pathlib
import sqlite3
import warnings
from collections.abc import Generator
from contextlib import asynccontextmanager
from types import FunctionType
from typing import Tuple
from typing import Union

import asyncpg
import fastapi
from da_common.client import Client as AuthenticatingClient
from da_common.config import Config
from druid_interactions.models.migration import Migration


def discover_scripts(package, prefix=""):
    "Find all Python scripts under the package path"
    for d in importlib.resources.files(package).iterdir():
        if d.is_dir():
            for i in d.iterdir():
                if i.suffix == ".py" and str(i.name).startswith(prefix):
                    yield i


def import_path(path):
    try:
        spec = importlib.util.spec_from_file_location(path.stem, path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
    except Exception as e:
        logging.getLogger("druid_interactions.migrations").exception(e)
        return None
    else:
        return module


def parse_version(text: str) -> tuple:
    "Convert a string into a normalised version attribute"
    if text and text.replace(".", "").isdigit():
        return tuple(int(i) for i in text.split("."))
    else:
        return None


def parse_path(path: Union[str, pathlib.Path]):
    "Create a comparison key from module file paths"
    path = pathlib.Path(path) if isinstance(path, str) else path
    parents = [
        parse_version(p.name) or str(p.name)
        for p in reversed(path.resolve().parents)
        if str(p.name)
    ]
    return parents + [path.name]


def is_action(obj):
    "Decide if a module attribute is a migration action"
    if isinstance(obj, FunctionType):
        if obj.__annotations__.get("return") == Generator[Tuple[str, dict]]:
            return True

    return False


def assemble_migrations(pkg_name):
    migrations = sorted(discover_scripts(pkg_name, prefix="sql"), key=parse_path)

    modules = {path: import_path(path) for path in migrations}
    return {
        p: {name: func for name, func in vars(m).items() if is_action(func)}
        for p, m in modules.items()
        if m
    }


async def action_migrations(
    action_name: str, actions: dict, pool, connection, note: str = "", **kwargs
):
    _ = ["SELECT", "INSERT", "UPDATE", "DELETE", "VALUES"]
    ddl = ["CREATE", "ALTER", "DROP", "TRUNCATE"]
    logger = logging.getLogger("druid_interactions.migrations.utils")

    for p, options in actions.items():
        logger.info(f"Reading {p}")
        version = parse_path(p)[-2]

        try:
            func = options[action_name]
        except KeyError:
            logger.info("Nothing to run")
            continue
        else:
            logger.info(f"Running function: {func.__name__}")

        for job, data in func():
            sql, args = pool.render(job, **data)
            logger.info(f"Version {version}: executing '{sql}' with {args=}")

            if any(i in sql for i in ddl):
                job = connection.execute(sql)
            else:
                job = connection.execute(sql, args)

            try:
                await job
                logger.info("OK")
            except asyncpg.exceptions.DuplicateObjectError:
                pass
            except asyncpg.exceptions.UndefinedObjectError:
                pass
            except (
                asyncpg.exceptions.PostgresSyntaxError,
                asyncpg.exceptions.UndefinedColumnError,
            ) as e:
                logger.error(f"{sql} - {e}")
            except sqlite3.OperationalError as e:
                logger.warning(f"Skipping '{sql}' due to {e!s}")

        record = Migration(version=version, note=note)
        sql, args = pool.render(record.sql_insert(), **record._values)
        try:
            await connection.execute(sql, *args)
        except (asyncpg.exceptions.UndefinedTableError, sqlite3.OperationalError):
            logger.warning("Migrations table is missing.")


class NonAuthenticatingClient(AuthenticatingClient):
    def passthrough_auth(self, request: fastapi.Request) -> None:
        return


class OfflineConfig(Config):
    "Enables database fixtures such that unit tests may run outside GCP."

    async def set_secret_config(self, db_secret_name: str) -> Config:
        self.db_config_valid = True

        # Try to connect to Postgres
        db_url = super().get_db_url("postgresql")
        try:
            conn = await asyncpg.connect(db_url)
            await conn.close()
        except Exception as e:
            warnings.warn(f"Postgres unavailable: {e=}")
            self.db_config_valid = False

        return self

    def get_db_url(self, scheme: str = "postgres", database: str = "test") -> str:
        if scheme.lower().startswith("postgres") and self.db_config_valid:
            return super().get_db_url(scheme)

        elif scheme.strip(":") == "memory":
            return ":memory:"
        else:
            # Construct a sqlite URI as per https://www.sqlite.org/uri.html
            path = self.data.get("db", {}).get("path", "")
            db = self.data.get("db", {}).get("database", database)
            suffix = self.data.get("db", {}).get("suffix", ".db")
            return (
                scheme
                and path
                and pathlib.Path(path).joinpath(db).with_suffix(suffix).expanduser().as_uri()
            )

    def build_clients(self, **kwargs) -> dict:
        return {
            k: NonAuthenticatingClient(base_url=v, **kwargs) for k, v in self.services.items()
        }


@asynccontextmanager
async def advisory_lock(connection, lock_a: int, lock_b: int):
    try:
        try:
            sql = "SELECT pg_advisory_lock($1, $2)"
            await connection.execute(sql, lock_a, lock_b)
        except Exception:
            raise
        else:
            acquired = True
        yield acquired
    finally:
        sql = "SELECT pg_advisory_unlock($1, $2)"
        await connection.execute(sql, lock_a, lock_b)


def add_migration_arguments(group: argparse._ArgumentGroup):
    group.add_argument(
        "--list", action="store_true", default=False, help="list the migrations available."
    )
    group.add_argument(
        "--read",
        action="store_true",
        default=False,
        help="Read which migration has been applied.",
    )
    group.add_argument(
        "--stop", required=False, default=None, help="Stop at migration version."
    )
    group.add_argument(
        "--note", required=False, default=None, help="Add a note to the migration change."
    )
    group.add_argument("--run", required=False, default=None, help="Supply an action command.")
    return group
