from collections.abc import Generator
from typing import <PERSON>ple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Adds a 'secret_id' column."
    yield (
        "ALTER TABLE druid_interactions.instances ADD COLUMN IF NOT EXISTS secret_id TEXT",
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Removes the 'secret_id' column."
    yield ("ALTER TABLE druid_interactions.instances DROP COLUMN IF EXISTS secret_id", {})
