import textwrap
from collections.abc import Generator
from typing import Tuple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Defines the druid_interactions.surveys table."
    yield (
        textwrap.dedent(
            """
        CREATE TABLE IF NOT EXISTS druid_interactions.surveys (
            survey_id       UUID NOT NULL,
            device_id       TEXT NOT NULL,
            report_type     TEXT NOT NULL,
            report          JSON DEFAULT '{}'::json,
            updated_at      timestamptz,
            PRIMARY KEY (survey_id),
            UNIQUE (device_id, report_type)
        )
        """
        ),
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Removes the druid_interactions.surveys table."
    yield ("DROP TABLE IF EXISTS druid_interactions.surveys", {})
