import textwrap
from collections.abc import Generator
from typing import Tuple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Add druid_alarm_id column to the druid_interactions.alarms table."
    yield (
        textwrap.dedent(
            """
            ALTER TABLE druid_interactions.alarms
            ADD COLUMN IF NOT EXISTS druid_alarm_id INTEGER;
        """
        ),
        {},
    )

    yield (
        textwrap.dedent(
            """
            ALTER TABLE druid_interactions.alarms
            ADD COLUMN IF NOT EXISTS description TEXT;
            """
        ),
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    yield (
        textwrap.dedent(
            """
            ALTER TABLE druid_interactions.alarms
            DROP COLUMN IF EXISTS druid_alarm_id;
        """
        ),
        {},
    )

    yield (
        textwrap.dedent(
            """
            ALTER TABLE druid_interactions.alarms
            DROP COLUMN IF EXISTS description;
            """
        ),
        {},
    )
