from collections.abc import Generator
from typing import <PERSON><PERSON>


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    """Migration to add unique constraint on druid_alarm_id."""

    yield ("TRUNCATE TABLE druid_interactions.alarms", {})

    # Step 1: Drop old constraint
    yield (
        "ALTER TABLE druid_interactions.alarms DROP CONSTRAINT IF EXISTS alarms_device_id_created_at_component_key;",
        {},
    )

    # drop alarm_id if it exists
    yield (
        "ALTER TABLE druid_interactions.alarms DROP COLUMN IF EXISTS alarm_id;",
        {},
    )

    yield (
        """
        DO $$
            BEGIN
            IF EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_schema = 'druid_interactions'
                AND table_name = 'alarms'
                AND column_name = 'druid_alarm_id'
            ) AND NOT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_schema = 'druid_interactions'
                AND table_name = 'alarms'
                AND column_name = 'alarm_id'
            )  THEN
                ALTER TABLE druid_interactions.alarms
                RENAME COLUMN druid_alarm_id TO alarm_id;
            END IF;
            END $$;
        """,
        {},
    )

    # Step 2: Add unique constraint with proper existence check
    yield (
        """
        DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'unique_alarm'
                ) THEN
                    ALTER TABLE druid_interactions.alarms
                    ADD CONSTRAINT unique_alarm
                    UNIQUE (device_id, alarm_type, alarm_id);
                END IF;
            END $$;
        """,
        {},
    )

    # Step 3: Create sequence
    yield (
        """
        CREATE SEQUENCE IF NOT EXISTS alarm_id_seq;
        """,
        {},
    )

    # Step 4: Alter alarm_id column to set its default to nextval from the sequence.
    yield (
        "ALTER TABLE druid_interactions.alarms ALTER COLUMN alarm_id SET DEFAULT nextval('alarm_id_seq');",
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    """Revert constraint changes."""

    # Drop new constraint
    yield (
        "ALTER TABLE druid_interactions.alarms DROP CONSTRAINT IF EXISTS unique_alarm;",
        {},
    )

    # Recreate old constraint using DO block for safety
    yield (
        """
        DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_constraint
                    WHERE conname = 'alarms_device_id_created_at_component_key'
                ) THEN
                    ALTER TABLE druid_interactions.alarms
                    ADD CONSTRAINT alarms_device_id_created_at_component_key
                    UNIQUE (device_id, created_at, component);
                END IF;
            END $$;
        """,
        {},
    )

    # rename alarm_id to druid_alarm_id
    yield (
        "ALTER TABLE druid_interactions.alarms RENAME COLUMN alarm_id TO druid_alarm_id;",
        {},
    )

    # Drop sequence
    yield (
        "DROP SEQUENCE IF EXISTS alarm_id_seq;",
        {},
    )

    # drop not null constraint on alarm_id
    yield (
        "ALTER TABLE druid_interactions.alarms ALTER COLUMN alarm_id DROP NOT NULL;",
        {},
    )
