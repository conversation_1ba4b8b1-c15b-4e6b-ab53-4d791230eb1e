import argparse
import asyncio
import getpass
import importlib.resources
import json
import logging
import pathlib
import platform
import sys

import druid_interactions
from da_common.config import Config
from druid_interactions.migrations.utils import action_migrations
from druid_interactions.migrations.utils import add_migration_arguments
from druid_interactions.migrations.utils import advisory_lock
from druid_interactions.migrations.utils import assemble_migrations
from druid_interactions.migrations.utils import parse_path
from druid_interactions.migrations.utils import parse_version
from druid_interactions.models.migration import Migration
from druid_interactions.pool import PoolFactory


LOCKS = (202404191, 202404192)


async def process_migrations(args, config, actions, pool):
    logger = logging.getLogger("migrations.process_migrations")

    async with pool.acquire() as connection:
        async with advisory_lock(connection, *LOCKS):

            sql, query_args = pool.render(Migration().sql_select())
            try:
                rows = await connection.fetch(sql, *query_args)
                latest = Migration(**dict(next(iter(rows))))
            except Exception as e:
                logging.error(e)
                latest = None

            if args.read:
                if not latest:
                    logger.info("No migrations in place")
                else:
                    logger.info(f"Latest DB migration: {latest.version} ({latest.note})")

            elif args.run:
                version = latest and tuple(latest.version) or (0, 0, 0)

                if args.run == "back":
                    stop = parse_version(args.stop)
                    if not stop:
                        logger.critical("Rollback requires explicit stop")
                        return 2

                    chosen = dict(
                        reversed(
                            [
                                (p, options)
                                for p, options in actions.items()
                                if stop < parse_path(p)[-2] <= version
                            ]
                        )
                    )
                else:
                    stop = parse_version(args.stop) or max(parse_path(i)[-2] for i in actions)
                    chosen = dict(
                        [
                            (p, options)
                            for p, options in actions.items()
                            if stop >= parse_path(p)[-2] > version
                        ]
                    )

                logger.info(
                    f"Running function '{args.run}' " f"from version {version} to {stop}."
                )
                logger.info(
                    f"Identified {len(chosen)} {'steps' if len(chosen) != 1 else 'step'} to run",
                )

                if chosen:
                    note = args.note if args.note is not None else getpass.getuser()
                    await action_migrations(
                        args.run, chosen, pool=pool, connection=connection, note=note
                    )


def parser(default_config=""):
    rv = argparse.ArgumentParser(usage=__doc__)
    rv.add_argument(
        "--config",
        required=False,
        type=pathlib.Path,
        default=default_config,
        help=f"Set the path to a TOML configuration file [{default_config}].",
    )
    add_migration_arguments(rv)
    return rv


def main(args):
    logging.basicConfig(
        style="{",
        format="{asctime}| {levelname:>8}| {name:<24} | {message}",
        level=logging.DEBUG,
        stream=sys.stderr,
    )
    logger = logging.getLogger("druid_interactions.migrations")
    logger.info(f"Druid Interactions Migrations version {druid_interactions.__version__}")

    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    config = loop.run_until_complete(Config.get_config(args.config))
    pool = loop.run_until_complete(PoolFactory.create(config))

    actions = assemble_migrations(druid_interactions.migrations)
    if args.list:
        with importlib.resources.as_file(
            importlib.resources.files(druid_interactions)
        ) as parent:
            output = {
                path.relative_to(parent).as_posix(): list(registered.keys())
                for path, registered in actions.items()
            }
        print(json.dumps(output, indent=2), file=sys.stdout)
    else:
        loop.run_until_complete(
            process_migrations(args=args, config=config, actions=actions, pool=pool)
        )
    return 0


def run():
    config = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
    p = parser(default_config=config)
    args = p.parse_args()
    rv = main(args)
    sys.exit(rv)


if __name__ == "__main__":
    run()
