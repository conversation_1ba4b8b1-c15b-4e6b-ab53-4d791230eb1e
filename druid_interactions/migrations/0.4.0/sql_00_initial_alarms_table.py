import textwrap
from collections.abc import Generator
from typing import Tuple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Correction to previous druid_interactions.alarms table PK."
    yield (
        textwrap.dedent(
            """
        CREATE TABLE IF NOT EXISTS druid_interactions.alarms (
            device_id       TEXT NOT NULL,
            alarm_type      TEXT NOT NULL,
            created_at      timestamptz,
            component       TEXT NOT NULL,
            event_id        UUID NOT NULL,
            event_type      TEXT,
            obj_id          INT,
            severity        TEXT NOT NULL,
            qualifier       TEXT,
            message         TEXT,
            cleared         BOOL,
            updated_at      timestamptz,
            published_at    timestamptz,
            PRIMARY KEY (event_id),
            UNIQUE (device_id, created_at, component)
        )
        """
        ),
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Removes the druid_interactions.alarms table."
    yield ("DROP TABLE IF EXISTS druid_interactions.alarms", {})
