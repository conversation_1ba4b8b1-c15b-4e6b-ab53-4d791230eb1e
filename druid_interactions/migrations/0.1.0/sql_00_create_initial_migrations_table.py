import textwrap
from collections.abc import Generator
from typing import <PERSON>ple


def apply(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Creates the first version of the druid_interactions.migrations table."
    yield ("CREATE SCHEMA IF NOT EXISTS druid_interactions", {})
    yield (
        textwrap.dedent(
            """
        CREATE TABLE IF NOT EXISTS druid_interactions.migrations (
            created_at      timestamptz,
            version INT[3],
            note    TEXT
        )
        """
        ),
        {},
    )


def back(*args, **kwargs) -> Generator[Tuple[str, dict]]:
    "Removes the druid_interactions.migrations table."
    yield ("DROP TABLE IF EXISTS druid_interactions.migrations", {})
    yield ("DROP SCHEMA IF EXISTS druid_interactions", {})
