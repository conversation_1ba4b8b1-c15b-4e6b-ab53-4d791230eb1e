import asyncio
import json
import logging
import sys
from collections import namedtuple

from da_common.config import Config
from dal_pubsub.pubsub import PubSub
from druid_interactions.creds import get_credentials
from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.enodebs import ENodeBReport
from druid_interactions.queries.system import SystemReport
from druid_interactions.reporter import Reporter


class Monitor(Reporter):
    Change = namedtuple("Change", ["arrive", "depart"], defaults=[None, None])

    @staticmethod
    def change(prior: list[int | str], active: list[int | str]) -> Change:
        prior = set(prior)
        active = set(active)
        return Monitor.Change(arrive=active - prior, depart=prior - active)

    def __init__(self, config: Config, pool=None, interval: int = 30):
        super().__init__(config=config, pool=pool, interval=interval)
        self.logger = logging.getLogger("druid_interactions.monitor")
        self.pubsub = None

    async def run(self, limit=sys.maxsize, loop=None):
        self.logger.info("Reporter coroutine running")
        self.pool = self.pool or await PoolFactory.create(self.config)
        self.logger.info(f"Using pool {self.pool}")

        while limit:
            if limit % 5 == 0:
                self.logger.info("Creating reports")

            if self.pubsub is None:
                try:
                    self.pubsub = PubSub(config=self.config, loop=loop)
                    self.pubsub.set_topic("nms-manifest")
                    self.logger.info("PubSub connected")
                    self.logger.info(f"Project: {self.pubsub.project_id}")
                    self.logger.info(f"Topic: {self.pubsub.topic}")
                except Exception as e:
                    self.logger.warning(f"Unable to connect to PubSub service: {e!s}")
                    await asyncio.sleep(self.interval)
                    continue

            instances = []
            async with self.pool.acquire() as connection:
                async with connection.transaction():
                    sql, args = self.pool.render(Instance(None, None).sql_select())
                    rows = await connection.fetch(sql, *args)
                    instances = [Instance(**dict(row)) for row in rows]

                for instance in instances:
                    self.logger.info(f"Accessing {instance.name}...")
                    creds = await get_credentials(self.config, secret_id=instance.secret_id)

                    for report_type in [ENodeBReport, SystemReport]:
                        report = report_type()
                        try:
                            data = await report.get_report_data(
                                instance, clients=self.clients, creds=creds, verify=False
                            )
                            result = report.build_report(data)
                            self.logger.info(f"{result=}")
                        except Exception as e:
                            self.logger.warning(f"{e!s}")
                            continue

                        query = Survey(
                            device_id=instance.name,
                            report_type=report_type.__name__,
                        )
                        try:
                            sql, args = self.pool.render(
                                query.sql_select(
                                    device_id=query.device_id, report_type=query.report_type
                                ),
                                **query._values,
                            )
                            rows = await connection.fetch(sql, *args)
                            if rows:
                                prior_survey = Survey(**dict(rows[0]))
                                prior_survey.report = json.loads(prior_survey.report)
                                prior = report_type.items(prior_survey.report)
                            else:
                                prior = {}
                        except Exception as e:
                            self.logger.warning(f"{e!s}")
                            continue

                        try:
                            survey = Survey(
                                device_id=instance.name,
                                report_type=report_type.__name__,
                                report=result,
                            )
                            sql, args = self.pool.render(survey.sql_insert(), **survey._values)
                            await connection.execute(sql, *args)
                        except Exception as e:
                            self.logger.warning(f"{e!s}")

                        active = report_type.items(survey.report)
                        change = self.change(prior, active)

                        for key in change.arrive:
                            item = active[key]
                            self.logger.info(f"Arriving {item=}")
                            try:
                                events = list(
                                    report_type.events(item, manifest_id=instance.name)
                                )
                            except AttributeError:
                                events = []
                                self.logger.info(f"{report_type} does not implement events")
                            for event in events:
                                try:
                                    event_type = event.pop("event_type")
                                    message = event_type(**event)
                                    payload = message.model_dump()
                                    _ = self.pubsub.push_payload(payload)
                                except Exception as e:
                                    self.logger.warning(f"{e!s}")

                        for key in change.depart:
                            item = prior[key]
                            self.logger.info(f"Departing {item=}")

            limit = limit - 1
            await asyncio.sleep(self.interval)
