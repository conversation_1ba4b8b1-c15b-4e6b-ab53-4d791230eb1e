"""trigger pubsub to resolve alarms in metrics collector

Revision ID: c63774cad059
Revises: 9b96c2b75c98
Create Date: 2025-04-23 22:18:43.468940

"""

import asyncio
import importlib
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Any

import nest_asyncio
from da_common.config import Config
from dal_pubsub.pubsub import PubSub
from sqlalchemy import URL
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, async_scoped_session
from sqlalchemy.ext.asyncio.engine import create_async_engine
from sqlalchemy.orm import sessionmaker

from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.watchdog import Watchdog

# revision identifiers, used by Alembic.
revision = "c63774cad059"
down_revision = "9b96c2b75c98"
branch_labels = None
depends_on = None


def get_db_url(config: Config, database_name: str | None = None) -> URL:
    """Get the database URL from the config."""
    return f"postgresql+asyncpg://postgres:M8azC5L5j*Zh#fb#@10.129.60.5:5432/{database_name}"

    config_db = config.data["db"]

    # reason it's not detecting the change "user -> username" in toml files, hence its a workaround
    # need to investigate
    if "user" in config_db:
        config_db["username"] = config_db["user"]
        del config_db["user"]

    config_db["database"] = database_name or config_db["database"]
    return URL.create(drivername="postgresql+asyncpg", **config_db)


def get_async_engine(async_db_url, schema_name: str | None = None):
    if schema_name:
        return create_async_engine(
            async_db_url,
            pool_pre_ping=True,
            pool_size=32,
            max_overflow=64,
            connect_args={"server_settings": {"search_path": schema_name}},
        )
    return create_async_engine(
        async_db_url,
        pool_pre_ping=True,
        pool_size=32,
        max_overflow=64,
    )


def get_async_session_maker(
    config: Config, database_name: str | None = None, schema_name: str | None = None
):
    async_db_url = get_db_url(config, database_name)
    async_engine = get_async_engine(async_db_url, schema_name)
    async_session_maker = sessionmaker(
        async_engine, expire_on_commit=False, class_=AsyncSession
    )
    return async_session_maker


@asynccontextmanager
async def get_async_session(
    session_factory: async_sessionmaker,
) -> AsyncGenerator[AsyncSession | Any, Any]:
    """scoped session context manager"""
    logger = logging.getLogger("session_scope")
    session = async_scoped_session(session_factory, scopefunc=asyncio.current_task)
    try:
        yield session
        await session.commit()
    except Exception as e:
        await session.rollback()
        logger.error(f"Session error: {str(e)}", exc_info=True)
        raise
    finally:
        await session.close()
        await session.remove()


def setup_alarms_pub_sub(config: Config) -> PubSub:
    """Set up the PubSub client for alarms.

    Args:
        config: Application configuration

    Returns:
        Configured PubSub client
    """
    pub_sub = PubSub(config=config)
    pub_sub.set_topic("nms-alarms")
    return pub_sub


async def get_mc_active_alarm_events(config: Config) -> list[dict]:
    """Get active alarm event IDs from a metrics collector.

    Args:
        config: Application configuration

    Returns:
        List of active alarm event IDs
    """
    async_session_maker = get_async_session_maker(config, database_name="metrics")
    async with get_async_session(async_session_maker) as async_session:
        query = text(
            """
            select distinct on (alarm_id) * from event
            where alarm_id in
                (
                    select internal_id from alarm
                    where object_type = 'DRUID'
                    and resolved is NULL
                ) order by alarm_id, created desc;
            """
        )

        result = await async_session.execute(query)
        events = result.mappings().all()

        # fetch alarms
        query = text(
            """
            select * from alarm
            where object_type = 'DRUID'
            and resolved is NULL
            """
        )
        result = await async_session.execute(query)
        alarms = result.mappings().all()

        alarm_internal_id_to_alarm = {alarm["internal_id"]: alarm for alarm in alarms}
        # create a list of tuples of (event, alarm), event alarm_id matches alarm internal_id
        alarm_events = []
        for event in events:
            alarm_events.append((dict(event), alarm_internal_id_to_alarm[event["alarm_id"]]))

        return alarm_events


async def close_existing_active_mc_alarms(
    pub_sub: PubSub,
    config: Config,
) -> None:
    """Close existing active alarms in metrics collector."""
    logger = logging.getLogger("close_existing_active_mc_alarms")

    mc_active_alarm_events = await get_mc_active_alarm_events(config)
    if not mc_active_alarm_events:
        logger.info("No active alarms found in metrics collector")
        pass

    logger.info(f"Found {len(mc_active_alarm_events)} druid active alarms in metrics collector")

    success_count = 0
    for event, alarm in mc_active_alarm_events:
        try:
            # Create a metrics collector alarm
            header = event["header"]
            data = event["data"]

            data["trendIndication"] = "resolved_alarm"
            del data["inventory"]

            # replace the sourceId, objectType, objectId, type with the values from the alarm
            header["sourceId"] = alarm["source_id"]
            data["objectType"] = alarm["object_type"]
            data["objectId"] = alarm["object_id"]
            data["type"] = alarm["type"]

            payload = {
                "header": header,
                "data": data,
            }
            # Push to PubSub
            pub_sub.push_payload(payload)
            success_count += 1

        except Exception as e:
            logger.error(f"Failed to process alarm {event['alarm_id']}: {str(e)}")

    logger.info(f"Successfully closed {success_count} alarms in metrics collector")


async def re_raise_alarms(
    pub_sub: PubSub,
    config: Config,
) -> None:
    """Re-raise alarms in metrics collector."""
    logger = logging.getLogger("reraise_alarms")

    # Use session for database operations
    async_session_maker = get_async_session_maker(
        config, database_name="druid_interactions", schema_name="druid_interactions"
    )
    async with get_async_session(async_session_maker) as async_session:
        # Get all alarms from the druid_interactions
        druid_interactions_alarms = await get_druid_interactions_alarms(async_session)
        if not druid_interactions_alarms:
            logger.info("No alarms found in druid_interactions")
            return

        alarm_entries = []
        for alarm in druid_interactions_alarms:
            alarm_entries.append(
                get_alarm_entry(alarm.get("device_id"), alarm.get("alarm_type"), alarm)
            )

        logger.info(f"Found {len(druid_interactions_alarms)} alarms to process")

    logger.info("Starting to re-raise alarms...")

    success_count = 0
    for alarm_entry in alarm_entries:
        try:
            payload = Watchdog.payload(alarm_entry)
            # Push to PubSub
            pub_sub.push_payload(payload)
            success_count += 1

        except Exception as e:
            logger.error(f"Failed to process alarm {alarm_entry.alarm_id}: {str(e)}")

    logger.info(f"Successfully re-raised {success_count} alarms to PubSub")


async def get_druid_interactions_alarms(async_session):
    stmt = text("SELECT * FROM alarms")
    result = await async_session.execute(stmt)
    rows = result.mappings().all()
    return [dict(row) for row in rows]


def get_alarm_entry(instance_name, alarm_type, item):
    identifier = item.get("extra_info", {}).get("identity") or item.get("obj_id")

    alarm_entry = AlarmEntry(
        device_id=instance_name,
        alarm_type=alarm_type,
        created_at=item.get("created_at"),
        component=item.get("component", ""),
        event_type=item.get("event_type"),
        obj_id=identifier,
        severity=item.get("severity", ""),
        message=item.get("message"),
        cleared=False,
    )
    alarm_entry.description = item.get("description")
    alarm_entry.alarm_id = item.get("alarm_id")
    alarm_entry.updated_at = item.get("updated_at")
    return alarm_entry


async def process_cleared_alarms() -> None:
    """Get cleared alarms that are active in metrics collector and send to PubSub."""
    logger = logging.getLogger("process_cleared_alarms")
    config_file = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
    config = await Config.get_config(config_file)

    if "test_" in config.data["db"]["database"]:
        logger.info("Skipping migration in test environment")
        return

    # explicitly set the project to dev-2 for now
    config.data["google"]["project"] = "nms-prod-1-952b"

    logger.info("Starting to process cleared alarms...")

    # Load configuration and setup
    pub_sub = setup_alarms_pub_sub(config)

    await close_existing_active_mc_alarms(pub_sub, config)
    await asyncio.sleep(2)

    await re_raise_alarms(pub_sub, config)
    await asyncio.sleep(2)


def upgrade() -> None:
    """Run the migration to close all active alarms in metrics collector and re raise them with new payload via pubsub"""

    # Patch the event loop to allow nested use
    nest_asyncio.apply()
    # Now we can safely run our async function
    asyncio.run(process_cleared_alarms())


def downgrade() -> None:
    """No downgrade action needed as this migration only sends alarms to pubsub."""
    pass


if __name__ == "__main__":
    asyncio.run(process_cleared_alarms())
