import unittest

from druid_interactions.queries.aspects import AspectsReport
from druid_interactions.queries.aspects import ENodeBCount
from druid_interactions.queries.aspects import IPRouteCount
from druid_interactions.queries.aspects import IPSecSecureAssociationCount
from druid_interactions.queries.aspects import NetDeviceCount
from druid_interactions.queries.aspects import PLMNCount
from druid_interactions.queries.aspects import RadioZoneCount
from druid_interactions.queries.aspects import S1ServerEnbCount
from druid_interactions.queries.aspects import SGwCount
from druid_interactions.queries.aspects import SGwSessionCount
from druid_interactions.queries.aspects import UsersCount
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.test.test_features import FeaturesTests


class AspectsTests(unittest.TestCase):

    api_replies = {
        "/api/features": FeaturesTests.data,
        "/api/count/enodeb": [6],
        "/api/count/ip_route": [12],
        "/api/count/ipsec_secure_association": [0],
        "/api/count/net_device": [10],
        "/api/count/plmn": [2],
        "/api/count/radio_zone": [0],
        "/api/count/sgw": [1],
        "/api/count/sgw_session": [2],
        "/api/count/s1_server_enb?oper_state=2": [1],
        "/api/count/subscriber": [100],
    }

    data = {
        ENodeBCount: api_replies["/api/count/enodeb"],
        FeaturesQuery: api_replies["/api/features"],
        IPRouteCount: api_replies["/api/count/ip_route"],
        IPSecSecureAssociationCount: api_replies["/api/count/ipsec_secure_association"],
        NetDeviceCount: api_replies["/api/count/net_device"],
        PLMNCount: api_replies["/api/count/plmn"],
        RadioZoneCount: api_replies["/api/count/radio_zone"],
        SGwCount: api_replies["/api/count/sgw"],
        SGwSessionCount: api_replies["/api/count/sgw_session"],
        S1ServerEnbCount: api_replies["/api/count/s1_server_enb?oper_state=2"],
        UsersCount: api_replies["/api/count/subscriber"],
    }

    def test_camel_to_snake_case(self):
        rv = AspectsReport.camel_to_snake_case("PLMNCount")
        self.assertEqual(rv, "plmn_count")

        rv = AspectsReport.camel_to_snake_case("IPSecSecureAssociationCount")
        self.assertEqual(rv, "ipsec_secure_association_count")

    def test_build_report(self):
        aspects = AspectsReport()
        report = aspects.build_report(self.data.copy())
        self.assertIsInstance(report, dict)
        self.assertEqual(len(aspects.queries), len(self.data))
        self.assertEqual(len(report), len(self.data) + 1)
        for key, value in report.items():
            with self.subTest(k=key, v=value):
                self.assertIsInstance(key, str)
                self.assertIsInstance(value, int)
                self.assertIn("_", key)
