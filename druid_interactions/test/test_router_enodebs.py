import unittest

import httpx
from asgi_lifespan import Life<PERSON><PERSON>anager
from da_common.security import get_current_user
from druid_interactions.main import build_app
from druid_interactions.models.survey import Survey
from druid_interactions.pool import pool_manager
from druid_interactions.queries.enodebs import ENodeBQuery
from druid_interactions.queries.enodebs import ENodeBReport
from druid_interactions.queries.enodebs import ENodeBTrxQuery
from druid_interactions.test.test_enodebs import ENodeBReportTests
from druid_interactions.test.test_migrations import TemporaryDBFixture


class ENodeBsRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    @staticmethod
    async def override_dependency():
        return {"scopes": ["nms"], "email": "<EMAIL>"}

    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)
        self.app.dependency_overrides[get_current_user] = self.override_dependency

    async def test_get_enodebs_by_event_id(self):
        queries = {
            ENodeBQuery: ENodeBReportTests.enodebs,
            ENodeBTrxQuery: ENodeBReportTests.enodeb_trxs,
        }
        report = ENodeBReport.build_report(queries)
        survey = Survey(
            device_id="dauk-mrl-green-druid-core", report_type="ENodeBReport", report=report
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.surveys")

                async with connection.transaction():
                    sql, args = self.app.state.pool.render(
                        survey.sql_insert(), **survey._values
                    )
                    await connection.execute(sql, *args)

                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/",
                    transport=transport,
                ) as test_client:
                    for cell_id in (
                        269056,
                        269057,
                        269058,
                        269312,
                        269335,
                        269338,
                        269501,
                        269502,
                    ):
                        with self.subTest(cell_id=cell_id):
                            url = f"nms/druid/enodebs/{cell_id}"
                            response = await test_client.get(url)
                            self.assertEqual(200, response.status_code, response)
                            data = response.json()
                            self.assertIsInstance(data, dict, url)
                            self.assertEqual(data.get("cell_id"), cell_id)
