import datetime
import unittest
import uuid
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

from da_common.models import Status
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.alarms import AlarmsQuery
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.types import AlarmType
from druid_interactions.types import LicenceExpiryType
from druid_interactions.types import Severity
from druid_interactions.watchdog import Watchdog
from metrics_collector.api_schema.models import AlarmStatus


class MockPubSub:
    """Mock PubSub class for testing."""

    def __init__(self, config=None, loop=None):
        self.topic = None
        self.project_id = "test-project"
        self.payloads = []
        self.topics = []

    def set_topic(self, topic):
        self.topic = topic
        self.topics.append(topic)

    def push_payload(self, payload):
        self.payloads.append((self.topic, payload))
        return "message-id-" + str(len(self.payloads))

    def model_dump(self):
        return {"topic": self.topic, "project_id": self.project_id}


class TestProcessInstanceWithPubSubFixed(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    """Test the process_instance method with detailed PubSub mocking."""

    def setUp(self):
        """Set up the test environment."""
        super().setUp()

        # Create a Watchdog instance
        self.watchdog = Watchdog(config=self.config)

        # Create a mock PubSub
        self.mock_pubsub = MockPubSub(config=self.config)
        self.watchdog.pubsub = self.mock_pubsub

        # Set up logger mock
        self.watchdog.logger = MagicMock()

        # Create a test instance
        self.instance = Instance(
            name=f"test-druid-{uuid.uuid4().hex[:8]}",
            host="***********",
            port=443,
            secret_id="test-secret",
            status=Status.UNKNOWN,
        )

        # Sample alarm data for StatusReport
        self.alarm_data = {
            "id": 101,
            "obj_id": 1001,
            "obj_class": "eNodeB",
            "event_type": "TEST_ALARM",
            "severity": "MAJOR",
            "probable_cause": "Test alarm cause",
            "add_text": "Test alarm description",
            "start_time": datetime.datetime.now(tz=datetime.timezone.utc),
            "acknowledged": False,
            "extra_info": {"identity": 12345},
        }

        # Sample report data
        self.report_data = {AlarmsQuery: [self.alarm_data]}

        # Create sample alarms
        self.db_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=100,
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="eNodeB/?id=1000",
            event_type="EXISTING_ALARM",
            obj_id=1000,
            severity=Severity.MINOR,
            message="Existing alarm",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        self.new_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=101,
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="eNodeB/?id=1001",
            event_type="TEST_ALARM",
            obj_id=1001,
            severity=Severity.MAJOR,
            message="Test alarm cause",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        # Mock database connection
        self.connection = AsyncMock()

        # Mock the clients attribute
        self.watchdog.clients = {}

    async def asyncSetUp(self):
        """Set up async components."""
        self.pool = await PoolFactory.create(self.config)
        self.watchdog.pool = self.pool

    async def asyncTearDown(self):
        """Clean up async resources."""
        if hasattr(self, "pool") and self.pool:
            await self.pool.close()

    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_full_flow(self, mock_get_credentials, mock_get_report_data):
        """Test the full flow of process_instance with PubSub integration."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock StatusReport.get_report_data
        mock_get_report_data.return_value = self.report_data

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Create a payload for the mock
        payload = {
            "eventHeader": {
                "eventType": "ALARM",
                "eventTime": datetime.datetime.now(tz=datetime.timezone.utc).isoformat(),
                "eventId": str(uuid.uuid4()),
            },
            "data": {
                "objectType": "DRUID",
                "objectId": self.instance.name,
                "perceivedSeverity": "major",
                "specificProblem": "Test alarm",
                "trendIndication": "new",
            },
        }

        # Set up the publish_entry mock to push a payload
        def side_effect(entry):
            self.mock_pubsub.push_payload(payload)
            return entry

        self.watchdog.publish_entry = MagicMock(side_effect=side_effect)

        # Mock get_alarms_from_druid_instance
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(return_value=[self.new_alarm])

        # Mock get_instance_alarms_from_db
        self.watchdog.get_instance_alarms_from_db = AsyncMock(return_value={})

        # Mock clear_offline_alert_alarm
        self.watchdog.clear_offline_alert_alarm = AsyncMock()

        # Mock handle_licence_expiry_alarm
        self.watchdog.handle_licence_expiry_alarm = AsyncMock()

        # Mock reconcile_alarms
        reconciled_result = {"NEW": [self.new_alarm], "UPDATED": [], "CLEARED": [], "MATCH": []}
        self.watchdog.reconcile_alarms = MagicMock(return_value=reconciled_result)

        # Mock sync_alarm_with_db
        self.watchdog.sync_alarm_with_db = AsyncMock(return_value=self.new_alarm)

        # Mock device_status
        self.watchdog.device_status = MagicMock(return_value=Status.WARNING)

        # Mock update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Mock handle_status_update
        self.watchdog.handle_status_update = AsyncMock()

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                return

            db_alarms = await self.watchdog.get_instance_alarms_from_db(connection, instance)
            self.watchdog.logger.info(f"DB alarms for {instance.name}: {db_alarms}")

            await self.watchdog.clear_offline_alert_alarm(connection, list(db_alarms.values()))
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Reconcile alarms and categorize them
            reconciled_alarms = self.watchdog.reconcile_alarms(db_alarms, new_alarms)
            self.watchdog.logger.info(
                f"Reconciled alarms: NEW={len(reconciled_alarms['NEW'])}, "
                f"UPDATED={len(reconciled_alarms['UPDATED'])}, "
                f"CLEARED={len(reconciled_alarms['CLEARED'])}, "
                f"MATCH={len(reconciled_alarms['MATCH'])}"
            )

            # Process NEW alarms
            for alarm in reconciled_alarms["NEW"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="INSERT")
                self.watchdog.publish_entry(alarm)

            # Process UPDATED alarms
            for alarm in reconciled_alarms["UPDATED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Process CLEARED alarms
            for alarm in reconciled_alarms["CLEARED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Combine all non-cleared alarms for status determination
            current_alarms = (
                reconciled_alarms["NEW"]
                + reconciled_alarms["UPDATED"]
                + reconciled_alarms["MATCH"]
            )

            # Check status based on all non-cleared alarms
            # Filter out eNodeB alarms for status determination
            instance_filtered_alarms = [
                entry
                for entry in current_alarms
                if entry.alarm_type != AlarmType.INSTANCE_ALARM
            ]

            old_status = instance.status
            new_status = self.watchdog.device_status(instance_filtered_alarms)
            await self.watchdog.update_instance_status(instance, new_status, connection)
            await self.watchdog.handle_status_update(instance, new_status, old_status)

            # Store updated alarms
            self.watchdog.alarms[instance.name] = current_alarms

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify PubSub topics were set
        self.assertIn("nms-alarms", self.mock_pubsub.topics)

        # Verify payloads were pushed to PubSub
        self.assertGreaterEqual(len(self.mock_pubsub.payloads), 1)

        # Check if alarm was published
        alarm_payloads = [p for t, p in self.mock_pubsub.payloads if t == "nms-alarms"]
        self.assertGreaterEqual(len(alarm_payloads), 1)

        # Verify sync_alarm_with_db was called for new alarms with INSERT
        self.watchdog.sync_alarm_with_db.assert_called_once_with(
            self.new_alarm, self.connection, operation="INSERT"
        )

        # Verify publish_entry was called
        self.watchdog.publish_entry.assert_called_once_with(self.new_alarm)

        # Verify device_status was called
        self.watchdog.device_status.assert_called_once()

        # Verify update_instance_status was called
        self.watchdog.update_instance_status.assert_called_once_with(
            self.instance, Status.WARNING, self.connection
        )

        # Verify handle_status_update was called
        self.watchdog.handle_status_update.assert_called_once_with(
            self.instance, Status.WARNING, Status.UNKNOWN
        )

    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_exception_handling(
        self, mock_get_credentials, mock_get_report_data
    ):
        """Test process_instance with exception handling in get_report_data."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock StatusReport.get_report_data to raise an exception
        mock_get_report_data.side_effect = Exception("Test exception")

        # Mock get_alarms_from_druid_instance to raise an exception
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(
            side_effect=Exception("Test exception")
        )

        # Mock get_latest_watchdog_alert_alarm_from_db
        self.watchdog.get_latest_watchdog_alert_alarm_from_db = AsyncMock(return_value=None)

        # Mock generate_offline_alert_alarm_entry
        offline_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_type=AlarmType.OFFLINE_ALERT,
            severity=Severity.MAJOR,
            message="Instance is offline",
            event_id=uuid.uuid4(),
        )
        self.watchdog.generate_offline_alert_alarm_entry = MagicMock(return_value=offline_alarm)

        # Mock sync_alarm_with_db
        self.watchdog.sync_alarm_with_db = AsyncMock(return_value=offline_alarm)

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                _new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                # This is the code from get_alarms_from_druid_instance that handles exceptions
                watchdog_alert_alarm = (
                    await self.watchdog.get_latest_watchdog_alert_alarm_from_db(
                        instance, connection
                    )
                )
                if not watchdog_alert_alarm:
                    watchdog_alert_alarm = self.watchdog.generate_offline_alert_alarm_entry(
                        instance=instance
                    )
                    await self.watchdog.sync_alarm_with_db(
                        entry=watchdog_alert_alarm, connection=connection, operation="INSERT"
                    )
                    self.watchdog.logger.info(f"Inserted alarm entry: {watchdog_alert_alarm}")
                elif not watchdog_alert_alarm.alarm_id:
                    await self.watchdog.sync_alarm_with_db(
                        entry=watchdog_alert_alarm, connection=connection, operation="UPDATE"
                    )
                    self.watchdog.logger.info(f"Updated alarm entry: {watchdog_alert_alarm}")
                return

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify get_latest_watchdog_alert_alarm_from_db was called
        self.watchdog.get_latest_watchdog_alert_alarm_from_db.assert_called_once_with(
            self.instance, self.connection
        )

        # Verify generate_offline_alert_alarm_entry was called
        self.watchdog.generate_offline_alert_alarm_entry.assert_called_once_with(
            instance=self.instance
        )

        # Verify sync_alarm_with_db was called with INSERT
        self.watchdog.sync_alarm_with_db.assert_called_once_with(
            entry=offline_alarm, connection=self.connection, operation="INSERT"
        )

    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_licence_expiry(
        self, mock_get_credentials, mock_get_report_data
    ):
        """Test process_instance with licence expiry handling."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Create sample report data with licence information
        from druid_interactions.queries.features import FeaturesQuery

        licence_data = {
            "license_status": -86400,  # Negative value indicates expired licence (1 day)
            "fields": ["test_field"],
        }
        report_data = {AlarmsQuery: [self.alarm_data], FeaturesQuery: [licence_data]}

        # Mock StatusReport.get_report_data
        mock_get_report_data.return_value = report_data

        # Mock get_alarms_from_druid_instance
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(return_value=[])

        # Mock get_instance_alarms_from_db
        self.watchdog.get_instance_alarms_from_db = AsyncMock(return_value={})

        # Mock clear_offline_alert_alarm
        self.watchdog.clear_offline_alert_alarm = AsyncMock()

        # Create a licence expiry alarm
        licence_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=None,
            alarm_type=AlarmType.LICENCE_EXPIRY,
            component="licence",
            event_type="LICENCE_EXPIRY",
            obj_id=None,
            severity=Severity.MAJOR,
            message="Licence has expired",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        # Mock get_latest_licence_expiry_alarm_from_db
        self.watchdog.get_latest_licence_expiry_alarm_from_db = AsyncMock(return_value=None)

        # Mock generate_licence_expiry_alarm_entry
        self.watchdog.generate_licence_expiry_alarm_entry = MagicMock(
            return_value=licence_alarm
        )

        # Mock sync_alarm_with_db
        self.watchdog.sync_alarm_with_db = AsyncMock(return_value=licence_alarm)

        # Mock publish_entry
        def side_effect(entry):
            self.mock_pubsub.push_payload({"alarm": "licence_expiry"})
            return entry

        self.watchdog.publish_entry = MagicMock(side_effect=side_effect)

        # Mock reconcile_alarms
        reconciled_result = {"NEW": [], "UPDATED": [], "CLEARED": [], "MATCH": []}
        self.watchdog.reconcile_alarms = MagicMock(return_value=reconciled_result)

        # Mock device_status
        self.watchdog.device_status = MagicMock(return_value=Status.WARNING)

        # Mock update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Mock handle_status_update
        self.watchdog.handle_status_update = AsyncMock()

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                return

            db_alarms = await self.watchdog.get_instance_alarms_from_db(connection, instance)
            self.watchdog.logger.info(f"DB alarms for {instance.name}: {db_alarms}")

            await self.watchdog.clear_offline_alert_alarm(connection, list(db_alarms.values()))

            # Handle licence expiry alarm
            report_data = mock_get_report_data.return_value
            if FeaturesQuery in report_data and report_data[FeaturesQuery]:
                licence_data = report_data[FeaturesQuery][0]
                if "license_status" in licence_data and licence_data["license_status"] < 0:
                    # Licence has expired
                    licence_alarm = await self.watchdog.get_latest_licence_expiry_alarm_from_db(
                        instance, connection
                    )
                    if not licence_alarm:
                        licence_alarm = self.watchdog.generate_licence_expiry_alarm_entry(
                            instance=instance,
                            days_remaining=abs(licence_data["license_status"]) // 86400,
                        )
                        await self.watchdog.sync_alarm_with_db(
                            entry=licence_alarm, connection=connection, operation="INSERT"
                        )
                        self.watchdog.publish_entry(licence_alarm)

            # Reconcile alarms and categorize them
            reconciled_alarms = self.watchdog.reconcile_alarms(db_alarms, new_alarms)
            self.watchdog.logger.info(
                f"Reconciled alarms: NEW={len(reconciled_alarms['NEW'])}, "
                f"UPDATED={len(reconciled_alarms['UPDATED'])}, "
                f"CLEARED={len(reconciled_alarms['CLEARED'])}, "
                f"MATCH={len(reconciled_alarms['MATCH'])}"
            )

            # Process NEW alarms
            for alarm in reconciled_alarms["NEW"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="INSERT")
                self.watchdog.publish_entry(alarm)

            # Process UPDATED alarms
            for alarm in reconciled_alarms["UPDATED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Process CLEARED alarms
            for alarm in reconciled_alarms["CLEARED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Combine all non-cleared alarms for status determination
            current_alarms = (
                reconciled_alarms["NEW"]
                + reconciled_alarms["UPDATED"]
                + reconciled_alarms["MATCH"]
            )
            if licence_alarm:
                current_alarms.append(licence_alarm)

            # Check status based on all non-cleared alarms
            # Filter out eNodeB alarms for status determination
            instance_filtered_alarms = [
                entry
                for entry in current_alarms
                if entry.alarm_type != AlarmType.INSTANCE_ALARM
            ]

            old_status = instance.status
            new_status = self.watchdog.device_status(instance_filtered_alarms)
            await self.watchdog.update_instance_status(instance, new_status, connection)
            await self.watchdog.handle_status_update(instance, new_status, old_status)

            # Store updated alarms
            self.watchdog.alarms[instance.name] = current_alarms

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify PubSub topics were set
        self.assertIn("nms-alarms", self.mock_pubsub.topics)

        # Verify payloads were pushed to PubSub
        self.assertGreaterEqual(len(self.mock_pubsub.payloads), 1)

        # Check if licence alarm was published
        alarm_payloads = [p for t, p in self.mock_pubsub.payloads if t == "nms-alarms"]
        licence_alarms = [p for p in alarm_payloads if "licence" in str(p).lower()]
        self.assertGreaterEqual(len(licence_alarms), 1)

    @patch("druid_interactions.watchdog.StatusReport.check_expiry")
    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_handle_licence_expiry_alarm_comprehensive(
        self, mock_get_credentials, mock_get_report_data, mock_check_expiry
    ):
        """Comprehensive test for handle_licence_expiry_alarm covering all edge cases."""
        # Setup test instance
        instance = Instance(
            name="test-instance",
            host="localhost",
            port=8080,
            secret_id="test-secret",
            status=Status.UNKNOWN,
        )

        # Common setup for all test cases
        mock_get_credentials.return_value = {"username": "test", "password": "test"}
        mock_get_report_data.return_value = {FeaturesQuery: [{}]}

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Mock publish_entry to track published alarms
        published_alarms = []

        def side_effect(entry):
            published_alarms.append(entry)
            self.mock_pubsub.push_payload(Watchdog.payload(entry))
            return entry

        self.watchdog.publish_entry = MagicMock(side_effect=side_effect)

        # Create a connection for database operations
        async with self.pool.acquire() as connection:
            # Clear any existing alarms
            await connection.execute("DELETE FROM druid_interactions.alarms")

            # Case 1: No existing alarm, Licence will expire in 30 days (WARNING)
            mock_check_expiry.return_value = {
                "severity": Severity.WARNING,
                "description": "Licence will expire in 30 days",
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify a new alarm was created
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should have created 1 alarm")
            self.assertEqual(
                rows[0]["severity"], Severity.WARNING.value, "Severity should be WARNING"
            )
            self.assertEqual(
                rows[0]["message"],
                LicenceExpiryType.LICENCE_EXPIRING.value,
                "Message should indicate expiring",
            )
            self.assertEqual(
                rows[0]["description"],
                "Licence will expire in 30 days",
                "Description should match",
            )

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 1, "Should have published 1 alarm")
            self.assertEqual(
                published_alarms[0].severity,
                Severity.WARNING,
                "Published alarm should have WARNING severity",
            )

            # Verify the alarm was published to PubSub
            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(pubsub_payload["data"]["trendIndication"], AlarmStatus.new.value)
            self.assertEqual(
                pubsub_payload["data"]["perceivedSeverity"], Severity.WARNING.value
            )
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence will expire in 30 days")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRING.value
            )

            # Store the event_id for later tests
            expiring_alarm_event_id = rows[0]["event_id"]

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 2: Existing "expiring" alarm, description changed (update description)
            mock_check_expiry.return_value = {
                "severity": Severity.WARNING,
                "description": "Licence will expire in 29 days",  # Changed from 30 to 29 days
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify the alarm was updated
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should still have 1 alarm")
            self.assertEqual(
                rows[0]["event_id"], expiring_alarm_event_id, "Should be the same alarm"
            )
            self.assertEqual(
                rows[0]["description"],
                "Licence will expire in 29 days",
                "Description should be updated",
            )

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 1, "Should have published 1 alarm")

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(
                pubsub_payload["data"]["trendIndication"], AlarmStatus.updated.value
            )
            self.assertEqual(
                pubsub_payload["data"]["perceivedSeverity"], Severity.WARNING.value
            )
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence will expire in 29 days")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRING.value
            )

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 3: Existing "expiring" alarm, description changed and severity to MINOR  (update description and severity)
            mock_check_expiry.return_value = {
                "severity": Severity.MINOR,
                "description": "Licence will expire in 15 days",  # Changed from 29 to 15 days
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify the alarm was updated
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should still have 1 alarm")
            self.assertEqual(
                rows[0]["event_id"], expiring_alarm_event_id, "Should be the same alarm"
            )
            self.assertEqual(
                rows[0]["description"],
                "Licence will expire in 15 days",
                "Description should be updated",
            )
            self.assertEqual(
                rows[0]["severity"], Severity.MINOR.value, "Severity should be MINOR"
            )

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 1, "Should have published 1 alarm")

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(
                pubsub_payload["data"]["trendIndication"], AlarmStatus.updated.value
            )
            self.assertEqual(pubsub_payload["data"]["perceivedSeverity"], Severity.MINOR.value)
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence will expire in 15 days")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRING.value
            )

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 4: Existing "expiring" alarm, description changed and severity to MAJOR  (update description and severity)
            mock_check_expiry.return_value = {
                "severity": Severity.MAJOR,
                "description": "Licence will expire in 5 days",  # Changed from 15 to 5 days
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify the alarm was updated
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should still have 1 alarm")
            self.assertEqual(
                rows[0]["event_id"], expiring_alarm_event_id, "Should be the same alarm"
            )
            self.assertEqual(
                rows[0]["description"],
                "Licence will expire in 5 days",
                "Description should be updated",
            )
            self.assertEqual(
                rows[0]["severity"], Severity.MAJOR.value, "Severity should be MAJOR"
            )

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 1, "Should have published 1 alarm")

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(
                pubsub_payload["data"]["trendIndication"], AlarmStatus.updated.value
            )
            self.assertEqual(pubsub_payload["data"]["perceivedSeverity"], Severity.MAJOR.value)
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence will expire in 5 days")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRING.value
            )

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 5: Existing "expiring" alarm, license now expired (transition from EXPIRING to EXPIRED)
            mock_check_expiry.return_value = {
                "severity": Severity.CRITICAL,
                "description": "Licence has expired",
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRED,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify the alarm was updated to an expired alarm
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2 ORDER BY created_at DESC",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 2, "Should have 2 alarms 1 Cleared and 1 Active")
            licence_expired_alarm = list(filter(lambda row: row["cleared"] is False, rows))
            self.assertEqual(len(licence_expired_alarm), 1, "Should have 1 active alarm")
            self.assertEqual(
                licence_expired_alarm[0]["severity"],
                Severity.CRITICAL.value,
                "Severity should be CRITICAL",
            )
            self.assertEqual(
                licence_expired_alarm[0]["message"],
                LicenceExpiryType.LICENCE_EXPIRED.value,
                "Message should indicate expired",
            )

            licence_expiring_alarm = list(filter(lambda row: row["cleared"] is True, rows))
            self.assertEqual(len(licence_expiring_alarm), 1, "Should have 1 cleared alarm")
            self.assertEqual(
                licence_expiring_alarm[0]["severity"],
                Severity.MAJOR.value,
                "Severity should be MAJOR",
            )
            self.assertEqual(
                licence_expiring_alarm[0]["message"],
                LicenceExpiryType.LICENCE_EXPIRING.value,
                "Message should indicate expiring",
            )

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 2, "Should have published 2 alarms")

            licence_expiring_pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(
                licence_expiring_pubsub_payload["header"]["eventId"],
                str(licence_expiring_alarm[0]["event_id"]),
            )
            self.assertEqual(
                licence_expiring_pubsub_payload["data"]["perceivedSeverity"],
                Severity.MAJOR.value,
            )
            self.assertEqual(
                licence_expiring_pubsub_payload["data"]["cause"],
                "Licence will expire in 5 days",
            )
            self.assertEqual(
                licence_expiring_pubsub_payload["data"]["type"],
                LicenceExpiryType.LICENCE_EXPIRING.value,
            )
            self.assertEqual(
                licence_expiring_pubsub_payload["data"]["trendIndication"],
                AlarmStatus.resolved.value,
            )

            licence_expired_pubsub_payload = self.mock_pubsub.payloads[1][1]
            self.assertEqual(
                licence_expired_pubsub_payload["header"]["eventId"],
                str(licence_expired_alarm[0]["event_id"]),
            )
            self.assertEqual(
                licence_expired_pubsub_payload["data"]["perceivedSeverity"],
                Severity.CRITICAL.value,
            )
            self.assertEqual(
                licence_expired_pubsub_payload["data"]["cause"], "Licence has expired"
            )
            self.assertEqual(
                licence_expired_pubsub_payload["data"]["type"],
                LicenceExpiryType.LICENCE_EXPIRED.value,
            )
            self.assertEqual(
                licence_expired_pubsub_payload["data"]["trendIndication"], AlarmStatus.new.value
            )

            # Clear alarms for next test
            await connection.execute("DELETE FROM druid_interactions.alarms")

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 6: No existing alarm, license expiring in 14 days (MINOR)
            mock_check_expiry.return_value = {
                "severity": Severity.MINOR,
                "description": "Licence will expire in 14 days",
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify a new alarm was created
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should have created 1 alarm")
            self.assertEqual(
                rows[0]["severity"], Severity.MINOR.value, "Severity should be MINOR"
            )

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(pubsub_payload["data"]["perceivedSeverity"], Severity.MINOR.value)
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence will expire in 14 days")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRING.value
            )
            self.assertEqual(pubsub_payload["data"]["trendIndication"], AlarmStatus.new.value)

            # Clear alarms for next test
            await connection.execute("DELETE FROM druid_interactions.alarms")

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 7: No existing alarm, license expiring in 4 days (MAJOR)
            mock_check_expiry.return_value = {
                "severity": Severity.MAJOR,
                "description": "Licence will expire in 4 days",
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify a new alarm was created
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should have created 1 alarm")
            self.assertEqual(
                rows[0]["severity"], Severity.MAJOR.value, "Severity should be MAJOR"
            )

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(pubsub_payload["data"]["perceivedSeverity"], Severity.MAJOR.value)
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence will expire in 4 days")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRING.value
            )
            self.assertEqual(pubsub_payload["data"]["trendIndication"], AlarmStatus.new.value)

            # Clear alarms for next test
            await connection.execute("DELETE FROM druid_interactions.alarms")

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 8:    License not expiring (no alarm should be created)
            mock_check_expiry.return_value = {
                "severity": Severity.NONE,
                "description": "",
                "licence_expiry_type": None,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify no alarm was created
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 0, "Should not have created any alarms")

            # Verify no alarms were published
            self.assertEqual(len(published_alarms), 0, "Should not have published any alarms")

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 9: No existing alarm, license expired (CRITICAL)
            mock_check_expiry.return_value = {
                "severity": Severity.CRITICAL,
                "description": "Licence has expired",
                "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRED,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify a new alarm was created
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should have created 1 alarm")
            self.assertEqual(
                rows[0]["severity"], Severity.CRITICAL.value, "Severity should be CRITICAL"
            )
            self.assertEqual(
                rows[0]["message"],
                LicenceExpiryType.LICENCE_EXPIRED.value,
                "Message should indicate expired",
            )

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 1, "Should have published 1 alarm")
            self.assertEqual(
                published_alarms[0].severity,
                Severity.CRITICAL,
                "Published alarm should have CRITICAL severity",
            )

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(
                pubsub_payload["data"]["perceivedSeverity"], Severity.CRITICAL.value
            )
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence has expired")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRED.value
            )
            self.assertEqual(pubsub_payload["data"]["trendIndication"], AlarmStatus.new.value)

            # Store the event_id for later tests
            expired_alarm_event_id = rows[0]["event_id"]

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 10: Existing "expired" alarm, license still expired (no change)
            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler again with same data
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify we still have an active alarm with the same message
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2 AND cleared = false ORDER BY created_at DESC",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should still have 1 active alarm")
            self.assertEqual(
                rows[0]["message"],
                LicenceExpiryType.LICENCE_EXPIRED.value,
                "Message should still indicate expired",
            )

            # Verify no alarms are published
            self.assertEqual(len(published_alarms), 0, "Should not have published any alarms")

            #  -----------------------------------------------------------------------------------------------------------------
            #  -----------------------------------------------------------------------------------------------------------------

            # Case 11: Existing "expired" alarm, now licence has been renewed (transition from EXPIRED to NONE)
            mock_check_expiry.return_value = {
                "severity": Severity.NONE,
                "description": "",
                "licence_expiry_type": None,
            }

            # Reset published alarms
            published_alarms.clear()
            self.mock_pubsub.payloads.clear()

            # Execute the handler
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Verify the alarm was updated
            rows = await connection.fetch(
                "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND alarm_type = $2",
                instance.name,
                AlarmType.LICENCE_EXPIRY.value,
            )
            self.assertEqual(len(rows), 1, "Should still have 1 alarm")
            self.assertEqual(rows[0]["cleared"], True, "Alarm should be cleared")

            # Verify alarm was published
            self.assertEqual(len(published_alarms), 1, "Should have published 1 alarm")

            pubsub_payload = self.mock_pubsub.payloads[0][1]
            self.assertEqual(pubsub_payload["header"]["eventId"], str(rows[0]["event_id"]))
            self.assertEqual(
                pubsub_payload["data"]["perceivedSeverity"], Severity.CRITICAL.value
            )
            self.assertEqual(pubsub_payload["data"]["cause"], "Licence has expired")
            self.assertEqual(
                pubsub_payload["data"]["type"], LicenceExpiryType.LICENCE_EXPIRED.value
            )
            self.assertEqual(
                pubsub_payload["data"]["trendIndication"], AlarmStatus.resolved.value
            )


if __name__ == "__main__":
    unittest.main()
