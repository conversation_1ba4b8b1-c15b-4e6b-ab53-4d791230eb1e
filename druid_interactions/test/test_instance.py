import unittest

from asgi_lifespan import LifespanManager
from druid_interactions.main import build_app
from druid_interactions.models.instance import Instance
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_migrations import TemporaryDBFixture


class InstanceTests(unittest.TestCase):
    def test_init(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        self.assertTrue(instance.created_at)
        self.assertTrue(instance.port)
        self.assertTrue(instance.role)

    def test_url(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        self.assertEqual(instance.url, "https://*************:443/api")


class DBTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)

    async def test_insert_instance(self):
        async with <PERSON>spanManager(self.app) as _manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")

                async with connection.transaction():
                    instance = Instance(name="dauk-mrl-green-druid", host="*************")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

                    sql, args = self.app.state.pool.render(
                        instance.sql_select(name=instance.name), **instance._values
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertTrue(rows)
                    self.assertEqual(rows[0]["created_at"], instance.created_at)

    async def test_delete_instance(self):
        async with LifespanManager(self.app) as _manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")

                async with connection.transaction():
                    instance = Instance(name="dauk-mrl-green-druid", host="*************")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

                    sql, args = self.app.state.pool.render(
                        instance.sql_delete(name=instance.name), **instance._values
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertTrue(rows)
                    self.assertEqual(rows[0]["created_at"], instance.created_at)

                    sql, args = self.app.state.pool.render(
                        instance.sql_select(), **instance._values
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertFalse(rows)
