import datetime
import unittest
from unittest.mock import AsyncMock
from unittest.mock import Mock

from druid_interactions.models.instance import Instance
from druid_interactions.queries.segw import IPSecCertificateQuery
from druid_interactions.queries.segw import IPSecChildConfigProposalQuery
from druid_interactions.queries.segw import IPSecChildConfigQuery
from druid_interactions.queries.segw import IPSecIKEConfigProposalQuery
from druid_interactions.queries.segw import IPSecPeerConfigQuery
from druid_interactions.queries.segw import IPSecPrivateKeyQuery
from druid_interactions.queries.segw import IPSecProposalQuery
from druid_interactions.queries.segw import IPSecSecureAssociationQuery
from druid_interactions.queries.segw import S1ClientQuery
from druid_interactions.queries.segw import SeGWReport


class IPSecChildConfigQueryTests(unittest.TestCase):
    schema = {"attributes": []}

    data = [
        {
            "admin_state": 1,
            "close_action": 2,
            "dpd_action": 2,
            "hostaccess": 0,
            "id": 1,
            "ipcomp": 0,
            "jitter": 2880,
            "lifetime": 86400,
            "mode": 2,
            "name": "tunnel1_child1",
            "rekeytime": 28800,
            "reqid": 0,
            "start_action": 2,
            "updown": "/usr/libexec/strongswan/_raemis_client_updown",
        },
        {
            "admin_state": 0,
            "close_action": 0,
            "dpd_action": 0,
            "hostaccess": 0,
            "id": 2,
            "ipcomp": 0,
            "jitter": 2880,
            "lifetime": 86400,
            "mode": 2,
            "name": "main-segw_child1",
            "rekeytime": 28800,
            "reqid": 0,
            "start_action": 0,
            "updown": "/usr/libexec/strongswan/_raemis_server_updown",
        },
    ]

    def test_normalize_ipsecchildconfig_data(self):
        rv = IPSecChildConfigQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertEqual(rv.get("id"), 1)

        for attr in (
            "jitter",
            "lifetime",
            "rekeytime",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), int)
                self.assertTrue(rv[attr])

        self.assertIsInstance(rv.get("name"), str)


class IPSecChildConfigProposalQueryTests(unittest.TestCase):
    schema = {"attributes": []}

    data = [
        {"child_cfg": 1, "id": 1, "prio": 1, "prop": 4},
        {"child_cfg": 2, "id": 5, "prio": 1, "prop": 6},
        {"child_cfg": 2, "id": 7, "prio": 1, "prop": 7},
    ]

    def test_normalize_ipsecchildconfigproposal_data(self):
        rv = IPSecChildConfigProposalQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertEqual(rv.get("id"), 1)

        for attr in (
            "id",
            "child_cfg",
            "prio",
            "prop",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), int)
                self.assertTrue(rv[attr])


class IPSecCertificateQueryTests(unittest.TestCase):
    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "END-ENTITY", "value": 0},
                    {"description": "ROOT-CA", "value": 1},
                    {"description": "SUB-CA", "value": 2},
                ],
                "default": 0,
                "help_text": "The type of the certificate. 0=End-Entity, 1=ROOT-CA, 2=SUB-CA",
                "name": "cert_type",
                "type": "integer",
                "unique": 0,
                "verbose_name": "cert type",
            },
        ]
    }

    data = [
        {
            "cert_type": 2,
            "expiry_date": "Dec 15 14:59:59 2032",
            "filename": "nzairspan.pem",
            "id": 3,
            "identity_set": 1,
            "issuer": "C=GB, O=Dense Air Ltd, CN=Root",
            "keytype": 2,
            "start_date": "Jan 16 15:29:22 2023",
            "subject": "C=GB, O=Dense Air Ltd, CN=NZ Airspan",
            "type": 1,
        },
        {
            "cert_type": 0,
            "expiry_date": "Dec 06 17:30:17 2025",
            "filename": "dauk-mrl-att-druid-nhe_server.pem",
            "id": 12,
            "identity_set": 1,
            "issuer": "C=GB, O=Dense Air Ltd, CN=NZ Airspan",
            "keytype": 2,
            "start_date": "Dec 06 17:30:17 2023",
            "subject": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-druid-nhe_server",
            "type": 1,
        },
    ]

    def test_normalize_ipseccertificate_data(self):
        rv = IPSecCertificateQuery.normalize_data(self.data[0], schema=self.schema)

        for attr in ("start_date", "expiry_date"):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), datetime.datetime)

        for attr in ("filename", "issuer", "subject"):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), str)


class IPSecIKEConfigProposalQueryTests(unittest.TestCase):
    schema = {"attributes": []}

    data = [
        {"ike_cfg": 1, "id": 1, "prio": 1, "prop": 4},
        {"ike_cfg": 2, "id": 5, "prio": 1, "prop": 6},
        {"ike_cfg": 2, "id": 7, "prio": 1, "prop": 7},
    ]

    def test_normalize_ipsecikeconfigproposal_data(self):
        rv = IPSecIKEConfigProposalQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertEqual(rv.get("id"), 1)

        for attr in (
            "id",
            "ike_cfg",
            "prio",
            "prop",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), int)
                self.assertTrue(rv[attr])


class IPSecPeerConfigQueryTests(unittest.TestCase):
    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "CLIENT", "value": 0},
                    {"description": "SERVER", "value": 1},
                ],
                "default": 0,
                "help_text": "The type of the peer_config. 0=Client, 1=Server",
                "name": "type",
                "type": "integer",
                "unique": 0,
                "verbose_name": "type",
            },
        ]
    }

    data = [
        {
            "auth_method": 1,
            "cert_policy": 1,
            "dpd_delay": 60,
            "eap_type": 0,
            "eap_vendor": 0,
            "id": 1,
            "ike_cfg": 1,
            "ike_version": 2,
            "jitter": 180,
            "key_identity": "5",
            "keyingtries": 20,
            "local_id": "4",
            "mediated_by": 0,
            "mediation": 0,
            "mobike": 1,
            "name": "tunnel1",
            "overtime": 172800,
            "peer_id": 0,
            "pool": "",
            "reauthtime": 0,
            "rekeytime": 86400,
            "remote_id": "1",
            "type": 0,
            "uniqueid": 0,
            "virtual": "::",
        },
        {
            "auth_method": 1,
            "cert_policy": 1,
            "dpd_delay": 120,
            "eap_type": 0,
            "eap_vendor": 0,
            "id": 2,
            "ike_cfg": 2,
            "ike_version": 2,
            "jitter": 180,
            "key_identity": "25",
            "keyingtries": 20,
            "local_id": "24",
            "mediated_by": 0,
            "mediation": 0,
            "mobike": 1,
            "name": "main-segw",
            "overtime": 172800,
            "peer_id": 0,
            "pool": "main-segw_pool",
            "reauthtime": 0,
            "rekeytime": 86400,
            "remote_id": "1",
            "type": 1,
            "uniqueid": 0,
            "virtual": "",
        },
    ]

    def test_normalize_ipsecpeerconfig_data(self):
        rv = IPSecPeerConfigQuery.normalize_data(self.data[1], schema=self.schema)

        self.assertEqual(rv.get("id"), 2)

        for attr in (
            "jitter",
            "ike_version",
            "rekeytime",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), int)
                self.assertTrue(rv[attr])

        self.assertEqual(rv.get("type"), "SERVER")
        self.assertEqual(rv.get("pool"), "main-segw_pool")


class IPSecPrivateKeyQueryTests(unittest.TestCase):
    schema = {"attributes": []}

    data = [
        {"filename": "dauk-mrl-att-druid-nhe.pkey", "id": 1, "identity_set": 1, "type": 1},
        {
            "filename": "dauk-mrl-att-druid-nhe_server.pkey",
            "id": 2,
            "identity_set": 1,
            "type": 1,
        },
    ]

    def test_normalize_ipsecprivatekey_data(self):
        rv = IPSecPrivateKeyQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertIsInstance(rv.get("type"), int)
        self.assertIsInstance(rv.get("filename"), str)


class IPSecSecureAssociationQueryTests(unittest.TestCase):
    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "help_text": "The current operational state of this secure association",
                "multiline": 0,
                "name": "oper_state",
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "bidirectional", "value": "bidirectional"},
                    {"description": "incoming", "value": "incoming"},
                    {"description": "outgoing", "value": "outgoing"},
                ],
                "default": "bidirectional",
                "help_text": "The direction of traffic affected by this secure association",
                "max_length": 16,
                "multiline": 0,
                "name": "direction",
                "type": "text",
                "unique": 0,
                "verbose_name": "direction",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "hmac-md5", "value": "hmac-md5"},
                    {"description": "hmac-sha1", "value": "hmac-sha1"},
                    {"description": "keyed-md5", "value": "keyed-md5"},
                    {"description": "keyed-sha1", "value": "keyed-sha1"},
                    {"description": "null", "value": "null"},
                    {"description": "hmac-sha2-256", "value": "hmac-sha2-256"},
                    {"description": "hmac-sha2-384", "value": "hmac-sha2-384"},
                    {"description": "hmac-sha2-512", "value": "hmac-sha2-512"},
                    {"description": "hmac-ripemd160", "value": "hmac-ripemd160"},
                    {"description": "aes-xcbc-mac", "value": "aes-xcbc-mac"},
                    {"description": "tcp-md5", "value": "tcp-md5"},
                ],
                "help_text": "",
                "max_length": 16,
                "name": "integrity_algorithm",
                "type": "text",
                "unique": 0,
                "verbose_name": "integrity algorithm",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "des-cbc", "value": "des-cbc"},
                    {"description": "3des-cbc", "value": "3des-cbc"},
                    {"description": "null", "value": "null"},
                    {"description": "blowfish-cbc", "value": "blowfish-cbc"},
                    {"description": "cast128-cbc", "value": "cast128-cbc"},
                    {"description": "des-deriv", "value": "des-deriv"},
                    {"description": "rijndael-cbc", "value": "rijndael-cbc"},
                    {"description": "aes-cbc", "value": "aes-cbc"},
                    {"description": "aes-ctr", "value": "aes-ctr"},
                    {"description": "aes-gcm-16", "value": "aes-gcm-16"},
                    {"description": "camellia-cbc", "value": "camellia-cbc"},
                ],
                "help_text": "",
                "max_length": 16,
                "multiline": 0,
                "name": "encryption_algorithm",
                "type": "text",
                "unique": 0,
                "verbose_name": "encryption algorithm",
            },
        ]
    }

    data = [
        {
            "creation_time": "2023-12-14 17:21:45",
            "direction": "bidirectional",
            "encryption_algorithm": "AES_CBC",
            "encryption_key": "",
            "id": 2,
            "ike_established": 0,
            "ike_reauth_time": 0,
            "ike_rekey_time": 86129,
            "integrity_algorithm": "HMAC_SHA2_256_12",
            "integrity_key": "",
            "local_addr": "**************",
            "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d",
            "local_spi": 3016139632,
            "local_ts": "fd00:1001::1/128",
            "local_vips": "fd00:1001::1",
            "name": "MarlowLab-IPv6-SeGW_child1",
            "oper_state": 2,
            "owner": "strongswan",
            "remote_addr": "**************",
            "remote_id": "mrl-att-ipv6segw",
            "remote_spi": 3016139632,
            "remote_ts": "fd00:1000::/64",
            "remote_vips": "",
            "sa_bytes_in": 11864,
            "sa_bytes_out": 506160,
            "sa_install_time": 108,
            "sa_life_time": 86292,
            "sa_mode": "TUNNEL",
            "sa_packets_in": 100,
            "sa_packets_out": 1489,
            "sa_protocol": "ESP",
            "sa_rekey_time": 27048,
        },
        {
            "creation_time": "2024-07-26 11:09:37",
            "direction": "bidirectional",
            "encryption_algorithm": "AES_CBC",
            "encryption_key": "",
            "id": 4,
            "ike_established": 0,
            "ike_reauth_time": 0,
            "ike_rekey_time": 47369,
            "integrity_algorithm": "HMAC_SHA2_256_12",
            "integrity_key": "",
            "local_addr": "**************",
            "local_id": "C=GB, O=Dense Air Ltd, CN=dauk-mrl-att-d",
            "local_spi": 2669715680,
            "local_ts": "fd00:1001::1/128",
            "local_vips": "fd00:1001::1",
            "name": "tunnel1_child1",
            "oper_state": 2,
            "owner": "strongswan",
            "remote_addr": "*************",
            "remote_id": "mrl-att-ipv6segw",
            "remote_spi": 2669715680,
            "remote_ts": "fd00:1000::/64",
            "remote_vips": "",
            "sa_bytes_in": 2779168,
            "sa_bytes_out": 2779056,
            "sa_install_time": 19058,
            "sa_life_time": 67342,
            "sa_mode": "TUNNEL",
            "sa_packets_in": 24814,
            "sa_packets_out": 24813,
            "sa_protocol": "ESP",
            "sa_rekey_time": 7957,
        },
    ]

    def test_normalize_ipsecsecureassociation_data(self):
        rv = IPSecSecureAssociationQuery.normalize_data(self.data[0], schema=self.schema)

        for attr in (
            "local_addr",
            "local_id",
            "name",
            "remote_addr",
            "remote_id",
            "oper_state",
            "direction",
            "integrity_algorithm",
            "encryption_algorithm",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), str)

        for attr in (
            "sa_bytes_in",
            "sa_bytes_out",
            "sa_packets_in",
            "sa_packets_out",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), int)

        self.assertIsInstance(rv.get("creation_time"), datetime.datetime)


class IPSecProposalQueryTests(unittest.TestCase):
    schema = {"attributes": []}

    data = [
        {"id": 1, "proposal": "aes128-sha1-modp2048"},
        {"id": 2, "proposal": "aes128-sha256-modp2048"},
        {"id": 3, "proposal": "aes256-sha1-modp2048"},
        {"id": 4, "proposal": "aes256-sha256-modp2048"},
        {"id": 6, "proposal": "aes256-sha256"},
        {"id": 7, "proposal": "aes256-sha1"},
    ]

    def test_normalize_ipsecproposal_data(self):
        for data in self.data:
            rv = IPSecProposalQuery.normalize_data(self.data[0], schema=self.schema)
            with self.subTest(data=data):
                self.assertIsInstance(rv.get("id"), int)
                self.assertIsInstance(rv.get("proposal"), str)


class S1ClientQueryTests(unittest.TestCase):
    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "help_text": "The current operational state of the managed object",
                "multiline": 0,
                "name": "oper_state",
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "locked", "value": 1},
                    {"description": "unlocked", "value": 2},
                    {"description": "shutdown", "value": 3},
                ],
                "default": 2,
                "help_text": "The administrative state of the managed object",
                "multiline": 0,
                "name": "admin_state",
                "type": "integer",
                "unique": 0,
                "verbose_name": "admin state",
            },
            {
                "can_be_null": 1,
                "choices": [
                    {"description": "Macro", "value": 0},
                    {"description": "Home", "value": 1},
                ],
                "default": 0,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "What type of enb are we using, a macro or home enb.",
                "multiline": 0,
                "name": "s1_client_type",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "unsignedint",
                "unique": 0,
                "verbose_name": "s1 client type",
            },
        ]
    }

    data = [
        {
            "admin_state": 2,
            "cell_identity": 269312,
            "connect_retry_time": 10,
            "default_route": 0,
            "emergency_area_list": "",
            "enb_name": "eNB-1052",
            "ep_group_id": 4,
            "external_ipv4": "",
            "external_ipv6": "",
            "id": 1,
            "local_port": 0,
            "max_downlink_bandwidth": 0,
            "max_uplink_bandwidth": 0,
            "mme1_ip": "***************",
            "mme1_port": 36412,
            "name": "eNB-1052",
            "oper_state": 2,
            "plmn_id": "00159",
            "raemis_id": 0,
            "relative_capacity_1": 255,
            "remote_gummeis": "00159:0:0",
            "report_last_record_id": 0,
            "report_periodicity": 60,
            "s1_client_type": 0,
            "s1_setup_timeout": 2000,
            "s1mme_net_device": "bond0.55",
            "sctp_read_count": 200,
            "support_emergency_calls": 1,
            "support_pws": 1,
            "tai_list": "00159:10",
            "topology_hiding": 0,
            "tos": 0,
            "ue_context_release_timeout": 3000,
        },
        {
            "admin_state": 2,
            "cell_identity": 269312,
            "connect_retry_time": 10,
            "default_route": 0,
            "emergency_area_list": "",
            "enb_name": "eNB-1052",
            "ep_group_id": 6,
            "external_ipv4": "",
            "external_ipv6": "",
            "id": 2,
            "local_port": 0,
            "max_downlink_bandwidth": 0,
            "max_uplink_bandwidth": 0,
            "mme1_ip": "fd00:1000::3",
            "mme1_port": 36412,
            "name": "315010-eNB-1052",
            "oper_state": 2,
            "plmn_id": "315010",
            "raemis_id": 0,
            "relative_capacity_1": 255,
            "remote_gummeis": "315010:0:0",
            "report_last_record_id": 0,
            "report_periodicity": 60,
            "s1_client_type": 0,
            "s1_setup_timeout": 2000,
            "s1mme_net_device": "gre_tunnel1_ch",
            "sctp_read_count": 200,
            "support_emergency_calls": 1,
            "support_pws": 1,
            "tai_list": "315010:10",
            "topology_hiding": 0,
            "tos": 0,
            "ue_context_release_timeout": 3000,
        },
    ]

    def test_normalize_s1client_data(self):
        rv = S1ClientQuery.normalize_data(self.data[0], schema=self.schema)

        for attr in (
            "admin_state",
            "oper_state",
            "s1_client_type",
            "plmn_id",
            "name",
            "enb_name",
            "cell_identity",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), str)

        for attr in (
            "max_downlink_bandwidth",
            "max_uplink_bandwidth",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), int)


class SeGWReportTests(unittest.IsolatedAsyncioTestCase):
    data = {
        IPSecChildConfigQuery: [
            IPSecChildConfigQuery.normalize_data(d, schema=IPSecChildConfigQueryTests.schema)
            for d in IPSecChildConfigQueryTests.data
        ],
        IPSecChildConfigProposalQuery: [
            IPSecChildConfigProposalQuery.normalize_data(
                d, schema=IPSecChildConfigProposalQueryTests.schema
            )
            for d in IPSecChildConfigProposalQueryTests.data
        ],
        IPSecCertificateQuery: [
            IPSecCertificateQuery.normalize_data(d, schema=IPSecCertificateQueryTests.schema)
            for d in IPSecCertificateQueryTests.data
        ],
        IPSecIKEConfigProposalQuery: [
            IPSecIKEConfigProposalQuery.normalize_data(
                d, schema=IPSecIKEConfigProposalQueryTests.schema
            )
            for d in IPSecIKEConfigProposalQueryTests.data
        ],
        IPSecPeerConfigQuery: [
            IPSecPeerConfigQuery.normalize_data(d, schema=IPSecPeerConfigQueryTests.schema)
            for d in IPSecPeerConfigQueryTests.data
        ],
        IPSecPrivateKeyQuery: [
            IPSecPrivateKeyQuery.normalize_data(d, schema=IPSecPrivateKeyQueryTests.schema)
            for d in IPSecPrivateKeyQueryTests.data
        ],
        IPSecSecureAssociationQuery: [
            IPSecSecureAssociationQuery.normalize_data(
                d, schema=IPSecSecureAssociationQueryTests.schema
            )
            for d in IPSecSecureAssociationQueryTests.data
        ],
        IPSecProposalQuery: [
            IPSecProposalQuery.normalize_data(d, schema=IPSecProposalQueryTests.schema)
            for d in IPSecProposalQueryTests.data
        ],
        S1ClientQuery: [
            S1ClientQuery.normalize_data(d, schema=S1ClientQueryTests.schema)
            for d in S1ClientQueryTests.data
        ],
    }

    def test_build_report(self):
        rv = SeGWReport.build_report(self.data)
        self.assertIsInstance(rv, dict)
        self.assertTrue(all(i.lower() == i for i in rv), rv)
        self.assertTrue(all(isinstance(i, list) for i in rv.values()), rv)
        self.assertFalse(any(not i for i in rv.values()), rv)

    async def test_get_report_data(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        report = SeGWReport()
        fixture = (
            (IPSecChildConfigQuery, IPSecChildConfigQueryTests),
            (IPSecChildConfigProposalQuery, IPSecChildConfigProposalQueryTests),
            (IPSecCertificateQuery, IPSecCertificateQueryTests),
            (IPSecIKEConfigProposalQuery, IPSecIKEConfigProposalQueryTests),
            (IPSecPeerConfigQuery, IPSecPeerConfigQueryTests),
            (IPSecPrivateKeyQuery, IPSecPrivateKeyQueryTests),
            (IPSecSecureAssociationQuery, IPSecSecureAssociationQueryTests),
            (IPSecProposalQuery, IPSecProposalQueryTests),
            (S1ClientQuery, S1ClientQueryTests),
        )
        for typ, test_case in fixture:
            report.queries[typ].get_object = AsyncMock()
            report.queries[typ].get_object.return_value = Mock()
            report.queries[typ].get_object.return_value.json.return_value = test_case.data

        result = await report.get_report_data(instance)
        self.assertTrue(all(i) for i in result.values())

        for typ, test_case in fixture:
            with self.subTest(typ=typ):
                report.queries[typ].get_object.assert_called_once()
