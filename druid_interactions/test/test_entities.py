import unittest.mock

from druid_interactions.models.instance import Instance
from druid_interactions.queries.entities import ENodeBQuery
from druid_interactions.queries.entities import ENodeBTrxQuery
from druid_interactions.queries.entities import EntitiesReport


class ENodeBQueryTests(unittest.TestCase):

    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The naming attribute",
                "multiline": 0,
                "name": "id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "delete"],
                "type": "unsignedint",
                "unique": 1,
                "verbose_name": "ID",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "help_text": "The current operational state of the managed " "object",
                "name": "oper_state",
                "net_device": 0,
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "locked", "value": 1},
                    {"description": "unlocked", "value": 2},
                    {"description": "shutdown", "value": 3},
                ],
                "default": 2,
                "help_text": "The administrative state of the managed object",
                "name": "admin_state",
                "net_device": 0,
                "type": "integer",
                "unique": 0,
                "verbose_name": "admin state",
            },
            {
                "can_be_null": 1,
                "choices": [
                    {"description": "Macro eNB", "value": 0},
                    {"description": "Home eNB", "value": 1},
                ],
                "default": 0,
                "help_text": "The eNB Type of this eNodeB.",
                "name": "enb_type",
                "type": "unsignedint",
                "unique": 0,
                "verbose_name": "enb type",
            },
        ]
    }

    data = [
        {
            "AntennaModel": 0,
            "Backup_Firmware_Version": "",
            "BootstrapFirmware_id": 0,
            "Description": "",
            "DeviceInfo_UpTime": 0,
            "FCCID": "",
            "Factory_Firmware_Version": "",
            "HardwareVersion": "",
            "LastResetReason": "",
            "Leds_Status": "",
            "Licenses_EnabledFeatures": "",
            "LocalTimeZone": "",
            "ModelName": "",
            "NTPFrequencyDisciplineEnabled": 0,
            "NTPServer1": "",
            "OperatorName": "",
            "PKI_Enable": 0,
            "PerfMgmt_Config_Enable": 0,
            "PerfMgmt_Config_PeriodicUploadTime": "",
            "PerfMgmt_Config_ReportingType": "",
            "PerfMgmt_MQTT_Config_SciSystemId": "",
            "PerfMgmt_MQTT_Config_keepalive": 0,
            "PeriodicInformInterval": 60,
            "ProductClass": "",
            "Running_Firmware_Version": "",
            "SampleSet_Enable": 0,
            "SecGWServer1": "",
            "SerialNumber": "",
            "SoftwareVersion": "",
            "SupportedSystems": "",
            "Temperature": 0,
            "Tenant": "",
            "TimeReference": "",
            "TunnelIpAddress": "",
            "acs_controlled": 0,
            "admin_state": 2,
            "en_dc_enabled": 0,
            "enb_type": 0,
            "id": 1,
            "identity": 1052,
            "last_inform_time": "",
            "leds_enabled": 0,
            "location_height": "",
            "location_latitude": "",
            "location_longitude": "",
            "name": "eNB-1052",
            "oper_state": 2,
            "plmn_id": "00159",
            "reported_tai_list": "00159:10",
            "sctp_address": "*************:48665",
            "secondary_plmn_id": "",
            "stream_id_allocation": 1,
        },
    ]

    def test_normalize_enodeb_data(self):
        rv = ENodeBQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertEqual(rv.get("id"), 1)

        for attr in (
            "PKI_Enable",
            "PerfMgmt_Config_Enable",
            "en_dc_enabled",
            "leds_enabled",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), bool)
                self.assertFalse(rv[attr])

        self.assertEqual(rv.get("admin_state"), "unlocked")
        self.assertEqual(rv.get("oper_state"), "enabled")
        self.assertEqual(rv.get("enb_type"), "Macro eNB")
        self.assertEqual(
            {i for i in rv if i[0].isupper()}, {"PKI_Enable", "PerfMgmt_Config_Enable"}
        )


class ENodeBTrxQueryTests(unittest.TestCase):

    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "help_text": "The naming attribute",
                "name": "id",
                "type": "unsignedint",
                "unique": 1,
                "verbose_name": "ID",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "help_text": "The current operational state of the managed " "object",
                "name": "oper_state",
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "locked", "value": 1},
                    {"description": "unlocked", "value": 2},
                    {"description": "shutdown", "value": 3},
                ],
                "default": 2,
                "help_text": "The administrative state of the managed object",
                "name": "admin_state",
                "type": "integer",
                "unique": 0,
                "verbose_name": "admin state",
            },
            {
                "can_be_null": 1,
                "choices": [
                    {"description": "1.4Mhz", "value": 7000},
                    {"description": "3Mhz", "value": 15000},
                    {"description": "5Mhz", "value": 25000},
                    {"description": "10Mhz", "value": 50000},
                    {"description": "15Mhz", "value": 75000},
                    {"description": "20Mhz", "value": 150000},
                ],
                "help_text": "The frequency spectrum bandwidth used by this "
                "radio head in the downlink direction",
                "name": "downlink_bandwidth",
                "type": "integer",
                "unique": 0,
                "verbose_name": "downlink bandwidth",
            },
            {
                "can_be_null": 1,
                "choices": [
                    {"description": "1.4Mhz", "value": 3500},
                    {"description": "3Mhz", "value": 7500},
                    {"description": "5Mhz", "value": 12500},
                    {"description": "10Mhz", "value": 25000},
                    {"description": "15Mhz", "value": 37500},
                    {"description": "20Mhz", "value": 50000},
                ],
                "help_text": "The frequency spectrum bandwidth used by this "
                "radio head in the uplink direction",
                "name": "uplink_bandwidth",
                "type": "integer",
                "unique": 0,
                "verbose_name": "uplink bandwidth",
            },
        ]
    }

    data = [
        {
            "admin_state": 2,
            "cell_id": 269334,
            "csg_access_mode": "Open Access",
            "csg_id": 0,
            "downlinkCaEnable": 0,
            "downlink_bandwidth": 150000,
            "downlink_earfcn": 0,
            "enb_id": 1,
            "handover_profile_id": 0,
            "id": 1,
            "location": "",
            "mac": "",
            "maxSirThr": 0,
            "mimo_group": 0,
            "minSirThr": 0,
            "name": "",
            "numRpInMaxQsv": 0,
            "oper_state": 2,
            "tac": 10,
            "uplink_bandwidth": 37500,
            "uplink_earfcn": 0,
        },
        {
            "admin_state": 2,
            "cell_id": 269570,
            "csg_access_mode": "Open Access",
            "csg_id": 0,
            "downlinkCaEnable": 0,
            "downlink_bandwidth": 150000,
            "downlink_earfcn": 0,
            "enb_id": 1,
            "handover_profile_id": 0,
            "id": 2,
            "location": "",
            "mac": "",
            "maxSirThr": 0,
            "mimo_group": 0,
            "minSirThr": 0,
            "name": "",
            "numRpInMaxQsv": 0,
            "oper_state": 2,
            "tac": 10,
            "uplink_bandwidth": 37500,
            "uplink_earfcn": 0,
        },
        {
            "admin_state": 2,
            "cell_id": 269336,
            "csg_access_mode": "Open Access",
            "csg_id": 0,
            "downlinkCaEnable": 0,
            "downlink_bandwidth": 150000,
            "downlink_earfcn": 0,
            "enb_id": 1,
            "handover_profile_id": 0,
            "id": 3,
            "location": "",
            "mac": "",
            "maxSirThr": 0,
            "mimo_group": 0,
            "minSirThr": 0,
            "name": "",
            "numRpInMaxQsv": 0,
            "oper_state": 2,
            "tac": 10,
            "uplink_bandwidth": 37500,
            "uplink_earfcn": 0,
        },
    ]

    def test_normalize_enodebtrx_data(self):
        rv = ENodeBTrxQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertEqual(rv.get("id"), 1)

        for attr in (
            "admin_state",
            "oper_state",
            "downlink_bandwidth",
            "uplink_bandwidth",
        ):
            with self.subTest(attr=attr):
                self.assertIsInstance(rv.get(attr), str)
                self.assertTrue(rv[attr])

        self.assertEqual(rv.get("admin_state"), "unlocked")
        self.assertEqual(rv.get("oper_state"), "enabled")
        self.assertEqual(rv.get("cell_id"), 269334)


class EntitiesReportTests(unittest.IsolatedAsyncioTestCase):

    data = {
        ENodeBQuery: [
            ENodeBQuery.normalize_data(d, schema=ENodeBQueryTests.schema)
            for d in ENodeBQueryTests.data
        ],
        ENodeBTrxQuery: [
            ENodeBTrxQuery.normalize_data(d, schema=ENodeBTrxQueryTests.schema)
            for d in ENodeBTrxQueryTests.data
        ],
    }

    def test_build_report(self):
        rv = EntitiesReport.build_report(self.data)
        self.assertIsInstance(rv, list)
        for enodeb_query in rv:
            self.assertIsInstance(enodeb_query.get("enb_trx"), list)

    async def test_get_report_data(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        report = EntitiesReport()
        for typ in (ENodeBQuery, ENodeBTrxQuery):
            report.queries[typ].get_object = unittest.mock.AsyncMock()
            report.queries[typ].get_object.return_value = unittest.mock.Mock()

        report.queries[
            ENodeBQuery
        ].get_object.return_value.json.return_value = ENodeBQueryTests.data
        report.queries[
            ENodeBTrxQuery
        ].get_object.return_value.json.return_value = ENodeBTrxQueryTests.data

        result = await report.get_report_data(instance)
        self.assertTrue(all(i) for i in result.values())

        report.queries[ENodeBQuery].get_object.assert_called_once()
        report.queries[ENodeBTrxQuery].get_object.assert_called_once()
