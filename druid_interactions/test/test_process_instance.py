import datetime
import unittest
import uuid
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

from da_common.models import Status
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.alarms import AlarmsQuery
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.types import AlarmType
from druid_interactions.types import Severity
from druid_interactions.watchdog import Watchdog


class TestProcessInstancePatchedMethod(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    """Test the process_instance method of the Watchdog class with a patched method."""

    def setUp(self):
        """Set up the test environment."""
        super().setUp()

        # Create a Watchdog instance with mocked dependencies
        self.watchdog = Watchdog(config=self.config)
        self.watchdog.pubsub = MagicMock()
        self.watchdog.logger = MagicMock()

        # Create a test instance
        self.instance = Instance(
            name=f"test-druid-{uuid.uuid4().hex[:8]}",
            host="***********",
            port=443,
            secret_id="test-secret",
            status=Status.UNKNOWN,
        )

        # Sample alarm data
        self.alarm_data = {
            "id": 101,
            "obj_id": 1001,
            "obj_class": "eNodeB",
            "event_type": "TEST_ALARM",
            "severity": "MAJOR",
            "probable_cause": "Test alarm cause",
            "add_text": "Test alarm description",
            "start_time": datetime.datetime.now(tz=datetime.timezone.utc),
            "acknowledged": False,
            "extra_info": {"identity": 12345},
        }

        # Sample report data
        self.report_data = {AlarmsQuery: [self.alarm_data]}

        # Create sample alarms
        self.db_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=100,
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="eNodeB/?id=1000",
            event_type="EXISTING_ALARM",
            obj_id=1000,
            severity=Severity.MINOR,
            message="Existing alarm",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        self.new_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=101,
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="eNodeB/?id=1001",
            event_type="TEST_ALARM",
            obj_id=1001,
            severity=Severity.MAJOR,
            message="Test alarm cause",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        # Mock database connection
        self.connection = AsyncMock()

    async def asyncSetUp(self):
        """Set up async components."""
        self.pool = await PoolFactory.create(self.config)
        self.watchdog.pool = self.pool

    async def asyncTearDown(self):
        """Clean up async resources."""
        if hasattr(self, "pool") and self.pool:
            await self.pool.close()

    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance(self, mock_get_credentials):
        """Test the process_instance method with a patched implementation."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock get_alarms_from_druid_instance
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(return_value=[self.new_alarm])

        # Mock get_instance_alarms_from_db
        self.watchdog.get_instance_alarms_from_db = AsyncMock(
            return_value={
                f"{self.db_alarm.alarm_type.value}_{self.db_alarm.alarm_id}": self.db_alarm
            }
        )

        # Mock clear_offline_alert_alarm
        self.watchdog.clear_offline_alert_alarm = AsyncMock()

        # Mock handle_licence_expiry_alarm
        self.watchdog.handle_licence_expiry_alarm = AsyncMock()

        # Mock reconcile_alarms
        reconciled_result = {
            "NEW": [],
            "UPDATED": [self.new_alarm],
            "CLEARED": [],
            "MATCH": [self.db_alarm],
        }
        self.watchdog.reconcile_alarms = MagicMock(return_value=reconciled_result)

        # Mock sync_alarm_with_db
        self.watchdog.sync_alarm_with_db = AsyncMock(return_value=self.new_alarm)

        # Mock publish_entry
        self.watchdog.publish_entry = MagicMock(return_value=self.new_alarm)

        # Mock device_status
        self.watchdog.device_status = MagicMock(return_value=Status.WARNING)

        # Mock update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Mock handle_status_update
        self.watchdog.handle_status_update = AsyncMock()

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                return

            db_alarms = await self.watchdog.get_instance_alarms_from_db(connection, instance)
            self.watchdog.logger.info(f"DB alarms for {instance.name}: {db_alarms}")

            await self.watchdog.clear_offline_alert_alarm(connection, list(db_alarms.values()))
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Reconcile alarms and categorize them
            reconciled_alarms = self.watchdog.reconcile_alarms(db_alarms, new_alarms)
            self.watchdog.logger.info(
                f"Reconciled alarms: NEW={len(reconciled_alarms['NEW'])}, "
                f"UPDATED={len(reconciled_alarms['UPDATED'])}, "
                f"CLEARED={len(reconciled_alarms['CLEARED'])}, "
                f"MATCH={len(reconciled_alarms['MATCH'])}"
            )

            # Process NEW alarms
            for alarm in reconciled_alarms["NEW"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="INSERT")
                self.watchdog.publish_entry(alarm)

            # Process UPDATED alarms
            for alarm in reconciled_alarms["UPDATED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Process CLEARED alarms
            for alarm in reconciled_alarms["CLEARED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Combine all non-cleared alarms for status determination
            current_alarms = (
                reconciled_alarms["NEW"]
                + reconciled_alarms["UPDATED"]
                + reconciled_alarms["MATCH"]
            )

            # Check status based on all non-cleared alarms
            # Filter out eNodeB alarms for status determination
            instance_filtered_alarms = [
                entry
                for entry in current_alarms
                if entry.alarm_type != AlarmType.INSTANCE_ALARM
            ]

            old_status = instance.status
            new_status = self.watchdog.device_status(instance_filtered_alarms)
            await self.watchdog.update_instance_status(instance, new_status, connection)
            await self.watchdog.handle_status_update(instance, new_status, old_status)

            # Store updated alarms
            self.watchdog.alarms[instance.name] = current_alarms

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify get_alarms_from_druid_instance was called
        self.watchdog.get_alarms_from_druid_instance.assert_called_once_with(
            self.instance, self.connection
        )

        # Verify get_instance_alarms_from_db was called
        self.watchdog.get_instance_alarms_from_db.assert_called_once_with(
            self.connection, self.instance
        )

        # Verify clear_offline_alert_alarm was called
        self.watchdog.clear_offline_alert_alarm.assert_called_once()

        # Verify handle_licence_expiry_alarm was called
        self.watchdog.handle_licence_expiry_alarm.assert_called_once_with(
            self.connection, self.instance
        )

        # Verify reconcile_alarms was called
        self.watchdog.reconcile_alarms.assert_called_once()

        # Verify sync_alarm_with_db was called for updated alarms
        self.watchdog.sync_alarm_with_db.assert_called_once_with(
            self.new_alarm, self.connection, operation="UPDATE"
        )

        # Verify publish_entry was called
        self.watchdog.publish_entry.assert_called_once_with(self.new_alarm)

        # Verify device_status was called
        self.watchdog.device_status.assert_called_once()

        # Verify update_instance_status was called
        self.watchdog.update_instance_status.assert_called_once_with(
            self.instance, Status.WARNING, self.connection
        )

        # Verify handle_status_update was called
        self.watchdog.handle_status_update.assert_called_once_with(
            self.instance, Status.WARNING, Status.UNKNOWN
        )

        # Verify alarms were stored
        self.assertEqual(len(self.watchdog.alarms[self.instance.name]), 2)

    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_exception(self, mock_get_credentials):
        """Test the process_instance method when get_alarms_from_druid_instance raises an exception."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock get_alarms_from_druid_instance to raise an exception
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(
            side_effect=Exception("Test exception")
        )

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                _new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                return

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify get_alarms_from_druid_instance was called
        self.watchdog.get_alarms_from_druid_instance.assert_called_once_with(
            self.instance, self.connection
        )

    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_new_alarms(self, mock_get_credentials):
        """Test the process_instance method with new alarms."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock get_alarms_from_druid_instance
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(return_value=[self.new_alarm])

        # Mock get_instance_alarms_from_db - empty to simulate no existing alarms
        self.watchdog.get_instance_alarms_from_db = AsyncMock(return_value={})

        # Mock clear_offline_alert_alarm
        self.watchdog.clear_offline_alert_alarm = AsyncMock()

        # Mock handle_licence_expiry_alarm
        self.watchdog.handle_licence_expiry_alarm = AsyncMock()

        # Mock reconcile_alarms - all alarms are new
        reconciled_result = {"NEW": [self.new_alarm], "UPDATED": [], "CLEARED": [], "MATCH": []}
        self.watchdog.reconcile_alarms = MagicMock(return_value=reconciled_result)

        # Mock sync_alarm_with_db
        self.watchdog.sync_alarm_with_db = AsyncMock(return_value=self.new_alarm)

        # Mock publish_entry
        self.watchdog.publish_entry = MagicMock(return_value=self.new_alarm)

        # Mock device_status
        self.watchdog.device_status = MagicMock(return_value=Status.WARNING)

        # Mock update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Mock handle_status_update
        self.watchdog.handle_status_update = AsyncMock()

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                return

            db_alarms = await self.watchdog.get_instance_alarms_from_db(connection, instance)
            self.watchdog.logger.info(f"DB alarms for {instance.name}: {db_alarms}")

            await self.watchdog.clear_offline_alert_alarm(connection, list(db_alarms.values()))
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Reconcile alarms and categorize them
            reconciled_alarms = self.watchdog.reconcile_alarms(db_alarms, new_alarms)
            self.watchdog.logger.info(
                f"Reconciled alarms: NEW={len(reconciled_alarms['NEW'])}, "
                f"UPDATED={len(reconciled_alarms['UPDATED'])}, "
                f"CLEARED={len(reconciled_alarms['CLEARED'])}, "
                f"MATCH={len(reconciled_alarms['MATCH'])}"
            )

            # Process NEW alarms
            for alarm in reconciled_alarms["NEW"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="INSERT")
                self.watchdog.publish_entry(alarm)

            # Process UPDATED alarms
            for alarm in reconciled_alarms["UPDATED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Process CLEARED alarms
            for alarm in reconciled_alarms["CLEARED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Combine all non-cleared alarms for status determination
            current_alarms = (
                reconciled_alarms["NEW"]
                + reconciled_alarms["UPDATED"]
                + reconciled_alarms["MATCH"]
            )

            # Check status based on all non-cleared alarms
            # Filter out eNodeB alarms for status determination
            instance_filtered_alarms = [
                entry
                for entry in current_alarms
                if entry.alarm_type != AlarmType.INSTANCE_ALARM
            ]

            old_status = instance.status
            new_status = self.watchdog.device_status(instance_filtered_alarms)
            await self.watchdog.update_instance_status(instance, new_status, connection)
            await self.watchdog.handle_status_update(instance, new_status, old_status)

            # Store updated alarms
            self.watchdog.alarms[instance.name] = current_alarms

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify sync_alarm_with_db was called for new alarms with INSERT
        self.watchdog.sync_alarm_with_db.assert_called_once_with(
            self.new_alarm, self.connection, operation="INSERT"
        )

        # Verify publish_entry was called
        self.watchdog.publish_entry.assert_called_once_with(self.new_alarm)

        # Verify device_status was called
        self.watchdog.device_status.assert_called_once()

        # Verify update_instance_status was called
        self.watchdog.update_instance_status.assert_called_once_with(
            self.instance, Status.WARNING, self.connection
        )

        # Verify handle_status_update was called
        self.watchdog.handle_status_update.assert_called_once_with(
            self.instance, Status.WARNING, Status.UNKNOWN
        )

        # Verify alarms were stored
        self.assertEqual(len(self.watchdog.alarms[self.instance.name]), 1)

    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_cleared_alarms(self, mock_get_credentials):
        """Test the process_instance method with cleared alarms."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock get_alarms_from_druid_instance - empty to simulate all alarms cleared
        self.watchdog.get_alarms_from_druid_instance = AsyncMock(return_value=[])

        # Mock get_instance_alarms_from_db
        self.watchdog.get_instance_alarms_from_db = AsyncMock(
            return_value={
                f"{self.db_alarm.alarm_type.value}_{self.db_alarm.alarm_id}": self.db_alarm
            }
        )

        # Mock clear_offline_alert_alarm
        self.watchdog.clear_offline_alert_alarm = AsyncMock()

        # Mock handle_licence_expiry_alarm
        self.watchdog.handle_licence_expiry_alarm = AsyncMock()

        # Create a cleared version of the db_alarm
        cleared_alarm = AlarmEntry(
            device_id=self.db_alarm.device_id,
            alarm_id=self.db_alarm.alarm_id,
            alarm_type=self.db_alarm.alarm_type,
            component=self.db_alarm.component,
            event_type=self.db_alarm.event_type,
            obj_id=self.db_alarm.obj_id,
            severity=self.db_alarm.severity,
            message=self.db_alarm.message,
            cleared=True,  # This alarm is now cleared
            event_id=self.db_alarm.event_id,
        )

        # Mock reconcile_alarms - all alarms are cleared
        reconciled_result = {"NEW": [], "UPDATED": [], "CLEARED": [cleared_alarm], "MATCH": []}
        self.watchdog.reconcile_alarms = MagicMock(return_value=reconciled_result)

        # Mock sync_alarm_with_db
        self.watchdog.sync_alarm_with_db = AsyncMock(return_value=cleared_alarm)

        # Mock publish_entry
        self.watchdog.publish_entry = MagicMock(return_value=cleared_alarm)

        # Mock device_status - no alarms means OK status
        self.watchdog.device_status = MagicMock(return_value=Status.OK)

        # Mock update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Mock handle_status_update
        self.watchdog.handle_status_update = AsyncMock()

        # Patch the process_instance method to avoid the transaction context manager
        original_process_instance = self.watchdog.process_instance

        async def patched_process_instance(instance, connection):
            try:
                new_alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )
            except Exception as e:
                self.watchdog.logger.warning(f"Error getting alarms for {instance.name}: {e}")
                return

            db_alarms = await self.watchdog.get_instance_alarms_from_db(connection, instance)
            self.watchdog.logger.info(f"DB alarms for {instance.name}: {db_alarms}")

            await self.watchdog.clear_offline_alert_alarm(connection, list(db_alarms.values()))
            await self.watchdog.handle_licence_expiry_alarm(connection, instance)

            # Reconcile alarms and categorize them
            reconciled_alarms = self.watchdog.reconcile_alarms(db_alarms, new_alarms)
            self.watchdog.logger.info(
                f"Reconciled alarms: NEW={len(reconciled_alarms['NEW'])}, "
                f"UPDATED={len(reconciled_alarms['UPDATED'])}, "
                f"CLEARED={len(reconciled_alarms['CLEARED'])}, "
                f"MATCH={len(reconciled_alarms['MATCH'])}"
            )

            # Process NEW alarms
            for alarm in reconciled_alarms["NEW"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="INSERT")
                self.watchdog.publish_entry(alarm)

            # Process UPDATED alarms
            for alarm in reconciled_alarms["UPDATED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Process CLEARED alarms
            for alarm in reconciled_alarms["CLEARED"]:
                await self.watchdog.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.watchdog.publish_entry(alarm)

            # Combine all non-cleared alarms for status determination
            current_alarms = (
                reconciled_alarms["NEW"]
                + reconciled_alarms["UPDATED"]
                + reconciled_alarms["MATCH"]
            )

            # Check status based on all non-cleared alarms
            # Filter out eNodeB alarms for status determination
            instance_filtered_alarms = [
                entry
                for entry in current_alarms
                if entry.alarm_type != AlarmType.INSTANCE_ALARM
            ]

            old_status = instance.status
            new_status = self.watchdog.device_status(instance_filtered_alarms)
            await self.watchdog.update_instance_status(instance, new_status, connection)
            await self.watchdog.handle_status_update(instance, new_status, old_status)

            # Store updated alarms
            self.watchdog.alarms[instance.name] = current_alarms

        # Replace the method with our patched version
        self.watchdog.process_instance = patched_process_instance

        # Call the patched method
        await self.watchdog.process_instance(self.instance, self.connection)

        # Restore the original method
        self.watchdog.process_instance = original_process_instance

        # Verify sync_alarm_with_db was called for cleared alarms with UPDATE
        self.watchdog.sync_alarm_with_db.assert_called_once_with(
            cleared_alarm, self.connection, operation="UPDATE"
        )

        # Verify publish_entry was called
        self.watchdog.publish_entry.assert_called_once_with(cleared_alarm)

        # Verify device_status was called
        self.watchdog.device_status.assert_called_once()

        # Verify update_instance_status was called with OK status
        self.watchdog.update_instance_status.assert_called_once_with(
            self.instance, Status.OK, self.connection
        )

        # Verify handle_status_update was called
        self.watchdog.handle_status_update.assert_called_once_with(
            self.instance, Status.OK, Status.UNKNOWN
        )

        # Verify alarms were stored (should be empty since all are cleared)
        self.assertEqual(len(self.watchdog.alarms[self.instance.name]), 0)


if __name__ == "__main__":
    unittest.main()
