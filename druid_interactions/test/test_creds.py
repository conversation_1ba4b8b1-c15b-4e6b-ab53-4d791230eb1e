import unittest.mock

from druid_interactions.creds import get_credentials


class CredentialTests(unittest.IsolatedAsyncioTestCase):
    async def test_basic_auth(self):
        credentials = {
            "username": "system",
            "password": "??????",
        }
        with unittest.mock.patch(
            "druid_interactions.creds.get_secret_data", return_value=credentials
        ):
            config = unittest.mock.Mock()
            creds = await get_credentials(config)
            self.assertEqual(creds, credentials)
