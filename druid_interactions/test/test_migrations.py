import asyncio
import importlib.resources
import pathlib
import platform
import tempfile
import unittest
import warnings

import druid_interactions
from druid_interactions.migrations.utils import OfflineConfig
from druid_interactions.migrations.utils import action_migrations
from druid_interactions.migrations.utils import assemble_migrations
from druid_interactions.migrations.utils import discover_scripts
from druid_interactions.migrations.utils import parse_path
from druid_interactions.migrations.utils import parse_version
from druid_interactions.pool import PoolFactory


class TemporaryDBFixture:
    def setUp(self):
        db_dir = None
        if platform.system() == "Windows":
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
            db_dir = pathlib.Path.cwd()

        path = importlib.resources.files("druid_interactions.cfg").joinpath("local.toml")
        self.config = asyncio.run(PoolFactory.get_config(path, config_class=OfflineConfig))

        if not self.config.db_config_valid:
            # Create a location for a file-based SQLite database
            name = self.id().split(".")[-1]
            self.temp_dir = pathlib.Path(
                tempfile.mkdtemp(prefix=f"{name}_", suffix="_tmp", dir=db_dir)
            )
            self.temp_dir.chmod(0o755)
            self.config.data.get("db", {})["path"] = self.temp_dir

    @staticmethod
    async def apply_migrations(pool, connection):
        actions = assemble_migrations(druid_interactions.migrations)
        await action_migrations("apply", actions, pool=pool, connection=connection)

    def tearDown(self):
        if not self.config.db_config_valid:
            try:
                for path in self.temp_dir.iterdir():
                    path.unlink(missing_ok=True)
                self.temp_dir.rmdir()
            except Exception as e:
                warnings.warn(f"{e}")


class VersionTests(unittest.TestCase):
    def test_version(self):
        self.assertEqual((0, 5, 0), parse_version("0.05.0"))
        self.assertEqual((0, 5, 0), parse_version("0.5.0"))
        self.assertEqual(None, parse_version("0.X.0"))


class DiscoveryTests(unittest.TestCase):
    def test_discovery(self):
        migrations = list(discover_scripts("druid_interactions.migrations"))
        self.assertTrue(migrations)


class OrderingTests(unittest.TestCase):
    def test_simple(self):
        path = "0.1.0/sql_0000_create_first_tables.py"
        key = parse_path(path)
        self.assertIsInstance(key[-2], tuple, key)
        self.assertTrue(all(isinstance(i, int) for i in key[-2]))

    def test_multi(self):
        paths = [
            "0.10.9/sql_0001_create_first_tables.py",
            "0.10.9/sql_0000_create_first_tables.py",
            "0.9.0/sql_0000_create_first_tables.py",
        ]
        ordered = sorted(paths, key=parse_path)
        self.assertEqual("0.9.0/sql_0000_create_first_tables.py", ordered[0])
        self.assertEqual("0.10.9/sql_0000_create_first_tables.py", ordered[1])
        self.assertEqual("0.10.9/sql_0001_create_first_tables.py", ordered[2])
