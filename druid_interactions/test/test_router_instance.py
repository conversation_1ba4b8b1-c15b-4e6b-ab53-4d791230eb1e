import unittest

import httpx
from asgi_lifespan import Lifespan<PERSON>anager
from da_common.models import Status
from druid_interactions.api import get_current_user
from druid_interactions.main import build_app
from druid_interactions.models.instance import Instance
from druid_interactions.models.instance import InstanceData
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.types import Role
from fastapi.encoders import jsonable_encoder


class InstanceRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    @staticmethod
    async def override_dependency():
        return {"scopes": ["nms"], "email": "<EMAIL>"}

    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)
        self.app.dependency_overrides[get_current_user] = self.override_dependency

    async def test_get_endpoint(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                async with connection.transaction():
                    instance = Instance(
                        name="dauk-mrl-green-druid",
                        host="*************",
                        port=443,
                        role=Role.NHE,
                        secret_id="druid-nhe-auth",
                    )
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "nms/druid/instances"
                response = await test_client.get(url)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertIsInstance(data, list)
                self.assertTrue(data)
                self.assertTrue(data[0].get("created_at"), data)

    async def test_delete_endpoint(self):
        instance = Instance(
            name="dauk-mrl-green-druid",
            host="*************",
            port=443,
            role=Role.NHE,
            secret_id="druid-nhe-auth",
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                async with connection.transaction():
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = f"nms/druid/instances/{instance.name}"
                response = await test_client.delete(url)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertIsInstance(data, dict, data)
                self.assertTrue(data.get("created_at"), data)

            async with self.app.state.pool.acquire() as connection:
                sql, args = self.app.state.pool.render(instance.sql_select())
                rows = await connection.fetch(sql, *args)
                self.assertFalse(rows)

    async def test_post_endpoint(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")

            instance_data = InstanceData(
                name="dauk-mrl-green-druid",
                host="*************",
                port=443,
                role=None,
                secret_id="druid-nhe-auth",
                status=Status.UNKNOWN,
            )
            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "nms/druid/instances"
                payload = jsonable_encoder(instance_data)
                response = await test_client.post(url, json=payload)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertTrue(data.get("created_at"), data)

            async with self.app.state.pool.acquire() as connection:
                sql, args = self.app.state.pool.render(Instance(**payload).sql_select())
                rows = await connection.fetch(sql, *args)
                self.assertTrue(rows)
                self.assertEqual(rows[0]["role"], Role.NONE.value, rows)
