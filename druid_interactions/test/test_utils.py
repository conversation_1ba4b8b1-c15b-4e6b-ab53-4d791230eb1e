import ipaddress
import json
import pprint

import pytest


@pytest.mark.skip("skipping frontend data tests")
def test_frontend_data():
    prefix_file_path = "test_data/core/networks/"
    # Load JSON data
    with open(prefix_file_path + "pdn.json") as f:
        pdn_data = json.load(f)
    with open(prefix_file_path + "network_slice.json") as f:
        network_slice_data = json.load(f)
    with open(prefix_file_path + "subscription_profile.json") as f:
        subscription_profile_data = json.load(f)
    with open(prefix_file_path + "net_device.json") as f:
        net_device_data = json.load(f)
    with open(prefix_file_path + "group.json") as f:
        group_data = json.load(f)
    with open(prefix_file_path + "ipv4_pool.json") as f:
        ipv4_pool_data = json.load(f)

    with open(prefix_file_path + "mgw_endpoint.json") as f:
        mgw_data = json.load(f)

    # Helper function to find matching entries
    def find_matching_entries(data, key, value):
        return [entry for entry in data if entry.get(key) == value]

    # Taken from Official Documentation page no 952
    device_type = {
        0: "unknown",
        1: "ethernet",
        2: "wifi",
        5: "bluetooth",
        6: "olpc mesh",
        7: "wimax",
        8: "modem",
        9: "infiniband",
        10: "bond",
        11: "vlan",
        12: "adsl",
        13: "bridge",
        14: "generic",
        16: "tun",
        18: "mac_vlan",
        20: "veth",
        23: "gre",
        24: "gretap",
        25: "ip6tnl",
        26: "ip6gre",
        22: "dummy",
    }

    # Construct the final response
    final_response = {}
    for pdn in pdn_data:
        # Find related network devices
        devices = find_matching_entries(net_device_data, "id", pdn["ipv4_pool_id"])
        # Find related subscription profiles
        profiles = find_matching_entries(subscription_profile_data, "apn", pdn["apn"])
        # Find related IPv4 pools (for user IPv4 assignment)
        ipv4_pools = find_matching_entries(ipv4_pool_data, "id", pdn["ipv4_pool_id"])
        # Find related groups (for user assignment)
        groups = find_matching_entries(group_data, "description", pdn["apn"])

        slices = None
        if profiles:
            profile = profiles[0]
            # Find related network slices
            slices = find_matching_entries(
                network_slice_data, "id", profile["network_slice_id"]
            )

        mgws = None
        if devices:
            device = devices[0]
            mgws = find_matching_entries(mgw_data, "net_device", device["device"])

        # Convert netmask to CIDR
        def netmask_to_cidr(netmask):
            return ipaddress.IPv4Network(f"0.0.0.0/{netmask}").prefixlen

        # Construct the response for each PDN
        response = {
            "Network Port": devices[0]["device"] if devices else None,
            "Network Slice": slices[0]["name"] if slices else None,
            "Accept All Users": ("NO" if profiles[0]["apply_to_all_subs"] == 0 else "YES")
            if profiles
            else None,
            "User IPv4 Assignment": (
                f"{ipv4_pools[0]['name']} : {ipv4_pools[0]['first_ip']} - {ipv4_pools[0]['last_ip']}"
                if ipv4_pools
                else None
            ),
            "Port IP": f"{devices[0]['ip']}/{netmask_to_cidr(devices[0]['netmask'])}"
            if devices
            else None,
            "Primary DNS": pdn["primary_dns"],
            "Primary IPv6 DNS": pdn["primary_ipv6_dns"],
            "Port Type": device_type.get(devices[0]["device_type"] if devices else None, None),
            "LAS": ("No" if mgws[0]["subnet_routing_enabled"] == 0 else "Yes")
            if mgws
            else None,
            "IPV4 Routes": f"{devices[0]['ip']}/{netmask_to_cidr(devices[0]['netmask'])}"
            if devices
            else None,
            "MAC Mappings": "Off" if pdn["use_mac_mappings"] == 0 else "On",
            "User Groups": groups[0]["description"] if groups else None,
            "Secondary DNS": pdn["secondary_dns"],
            "Secondary IPv6 DNS": pdn["secondary_ipv6_dns"],
            "State": "UP" if devices and devices[0]["oper_state"] == 2 else "DOWN",
            "Port IP Config": devices[0]["bootproto"] if devices else None,
            # TODO: Currently api ipv6_prefix_pool returns empty list , so dont know its payload format
            "User IPv6 Assignment": None,
        }
        final_response[pdn["apn"]] = response

    pprint.pprint(final_response)


if __name__ == "__main__":
    test_frontend_data()
