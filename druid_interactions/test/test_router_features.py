import unittest.mock
import urllib.parse

import httpx
from asgi_lifespan import Lifespan<PERSON>anager
from druid_interactions.api import get_current_user
from druid_interactions.main import build_app
from druid_interactions.models.instance import Instance
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.test.test_router_instance import InstanceRouterTests
from fastapi.encoders import jsonable_encoder


class FeaturesRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):

    api_schema_reply = {
        "attributes": [
            {
                "can_be_null": 0,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The naming attribute",
                "multiline": 0,
                "name": "id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "unsignedint",
                "verbose_name": "ID",
            },
            {
                "can_be_null": 0,
                "default": 0,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The product ID covered by this license.",
                "multiline": 0,
                "name": "product_id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "integer",
                "verbose_name": "product id",
            },
            {
                "can_be_null": 0,
                "default": "",
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "Unique identifier of this Raemis instance.",
                "max_length": 1024,
                "multiline": 0,
                "name": "system_id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "text",
                "verbose_name": "system id",
            },
            {
                "can_be_null": 0,
                "default": "",
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The serial number of the issued license.",
                "max_length": 128,
                "multiline": 0,
                "name": "license_id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "text",
                "verbose_name": "license id",
            },
            {
                "can_be_null": 0,
                "default": 0,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The number of seconds that the license key is "
                "valid for. 0 indicates that the current license "
                "is available indefinitley.",
                "multiline": 0,
                "name": "license_status",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "integer",
                "verbose_name": "license status",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The date that this license was issued",
                "multiline": 0,
                "name": "issue_date",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "datetime",
                "verbose_name": "issue date",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The expiry date of the license",
                "multiline": 0,
                "name": "expiry_date",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "datetime",
                "verbose_name": "expiry date",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The date that this license was bound by the " "license manager",
                "multiline": 0,
                "name": "binding_date",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "datetime",
                "verbose_name": "binding date",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The date that license is supported until",
                "multiline": 0,
                "name": "supported_until",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "datetime",
                "verbose_name": "supported until",
            },
            {
                "can_be_null": 0,
                "default": 0,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "Indicates if license is a Demo License",
                "multiline": 0,
                "name": "demo_license",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "boolean",
                "verbose_name": "demo license",
            },
        ],
        "display_name_attributes": ["id"],
        "dynamic_update": 0,
        "filter_attrs": ["id", "uuid"],
        "name": "features",
        "primitives": ["get", "get_csv"],
    }

    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)
        self.app.dependency_overrides[
            get_current_user
        ] = InstanceRouterTests.override_dependency

    async def test_licence_endpoint_instance_gone(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")

            transport = httpx.ASGITransport(app=manager.app)

            async with httpx.AsyncClient(
                base_url="http://localhost/", transport=transport
            ) as client:

                url = "nms/druid/dauk-mrl-green-druid/features/licence"
                response = await client.get(url)
                self.assertEqual(410, response.status_code, response.json())
                data = response.json()
                self.assertIsInstance(data, dict)
                self.assertTrue(data)

    async def test_licence_endpoint(self):
        credentials = {
            "username": "system",
            "password": "??????",
        }
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        instance_urls = []

        def fake_response(request):
            url = str(request.url)
            instance_urls.append(url)
            if "schema" in url:
                output = jsonable_encoder(self.api_schema_reply)
            else:
                output = jsonable_encoder(
                    [
                        {
                            "license_id": "ABCDEF01234567",
                            "license_status": 60 * 60 * 12,
                        }
                    ]
                )
            return httpx.Response(200, json=output)

        transport = httpx.MockTransport(fake_response)
        self.app.state.clients[instance.name] = httpx.AsyncClient(
            base_url=instance.url, transport=transport
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            with unittest.mock.patch(
                "druid_interactions.creds.get_secret_data", return_value=credentials
            ) as mock_secret_coro:
                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/", transport=transport
                ) as client:

                    url = f"nms/druid/{instance.name}/features/licence"
                    response = await client.get(url)

                    mock_secret_coro.assert_called_once()
                    self.assertEqual(len(instance_urls), 2)
                    for n, url in enumerate(instance_urls):
                        with self.subTest(n=n, url=url):
                            self.assertTrue(
                                urllib.parse.urlparse(url).netloc.startswith("*************")
                            )
                            if n == 0:
                                self.assertEqual(
                                    urllib.parse.urlparse(url).path, "/api/features"
                                )
                            else:
                                self.assertEqual(
                                    urllib.parse.urlparse(url).path, "/api/schema/features"
                                )

                    self.assertEqual(200, response.status_code, response.json())
                    data = response.json()
                    self.assertIsInstance(data, dict)
                    self.assertTrue(data)

    async def test_licence_endpoint_unreachable(self):
        credentials = {
            "username": "system",
            "password": "??????",
        }
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        instance_urls = []

        def fake_response(request):
            url = str(request.url)
            instance_urls.append(url)
            return httpx.Response(500)

        transport = httpx.MockTransport(fake_response)
        self.app.state.clients[instance.name] = httpx.AsyncClient(
            base_url=instance.url, transport=transport
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            with unittest.mock.patch(
                "druid_interactions.creds.get_secret_data", return_value=credentials
            ) as _mock_secret_coro:
                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/", transport=transport
                ) as client:

                    url = f"nms/druid/{instance.name}/features/licence"
                    response = await client.get(url)
                    self.assertEqual(response.status_code, 503)
