import datetime
import unittest.mock
import uuid

import httpx
from asgi_lifespan import LifespanManager
from da_common.security import get_current_user
from druid_interactions.main import build_app
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.types import AlarmType
from druid_interactions.types import Severity
from druid_interactions.watchdog import Watchdog


class AlarmsRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    entries = [
        AlarmEntry(
            "dauk-mrl-green-druid",
            alarm_type=AlarmType.OFFLINE_ALERT,
            severity=Severity.MAJOR,
            component="watchdog",
        ),
        AlarmEntry(
            "dauk-mrl-green-druid",
            alarm_type=AlarmType.LICENCE_EXPIRY,
            severity=Severity.MINOR,
            component="licence",
        ),
        AlarmEntry(
            "dauk-mrl-green-druid",
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="features",
            severity=Severity.CRITICAL,
        ),
    ]

    @staticmethod
    async def override_dependency():
        return {"scopes": ["nms"], "email": "<EMAIL>"}

    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)
        self.app.dependency_overrides[get_current_user] = self.override_dependency

    async def test_get_alarms(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.alarms")

                async with connection.transaction():
                    for entry in self.entries:
                        sql, args = entry.sql_insert()
                        await connection.execute(sql, *args)

                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/",
                    transport=transport,
                ) as test_client:
                    url = "nms/druid/alarms/dauk-mrl-green-druid"
                    response = await test_client.get(url)
                    self.assertEqual(200, response.status_code, response)
                    data = response.json()
                    self.assertIsInstance(data, list)
                    self.assertEqual(len(data), 3)

    async def test_get_alarms_by_event_id(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.alarms")

                async with connection.transaction():
                    for entry in self.entries:
                        sql, args = entry.sql_insert()
                        await connection.execute(sql, *args)

                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/",
                    transport=transport,
                ) as test_client:
                    url = f"nms/druid/alarms/dauk-mrl-green-druid/{self.entries[0].event_id}"
                    response = await test_client.get(url)
                    self.assertEqual(200, response.status_code, response)
                    data = response.json()
                    self.assertIsInstance(data, dict, url)
                    self.assertEqual(uuid.UUID(data.get("event_id")), self.entries[0].event_id)

    def test_alarm_payload(self):
        # Create a test alarm
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        alarm = AlarmEntry(
            "dauk-mrl-green-druid",
            alarm_type=AlarmType.OFFLINE_ALERT,
            severity=Severity.MAJOR,
            component="watchdog",
            message="test_watchdog_1",
            created_at=now,
            updated_at=now,
            published_at=now,
        )

        # Test new alarm payload
        payload = Watchdog.payload(alarm)
        self.assertEqual(payload["data"].get("trendIndication"), "new")
        self.assertEqual(payload["data"].get("objectType"), "DRUID")

        # Test cleared alarm payload
        alarm.cleared = True
        payload = Watchdog.payload(alarm)
        self.assertEqual(payload["data"].get("trendIndication"), "resolved")

        # Test updated alarm payload
        alarm.cleared = False
        alarm.updated_at = alarm.created_at + datetime.timedelta(seconds=10)
        payload = Watchdog.payload(alarm)
        self.assertEqual(payload["data"].get("trendIndication"), "updated")
