import unittest.mock

from druid_interactions.queries.sessions import P<PERSON><PERSON><PERSON><PERSON>y
from druid_interactions.queries.sessions import <PERSON><PERSON><PERSON><PERSON>
from druid_interactions.queries.sessions import SGwQuery
from druid_interactions.queries.sessions import SGwSessionQuery


class PLMNQueryTests(unittest.TestCase):

    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {
                        "description": "Global config - use the "
                        "attribute in this object for all "
                        "subscribers",
                        "value": 0,
                    },
                    {
                        "description": "Subscriber Msisdn - the "
                        "subscribers private cellular "
                        "number",
                        "value": 1,
                    },
                    {
                        "description": "Subscriber Msisdn and Call "
                        "Divert - the subscribers private "
                        "cellular number with an "
                        "indication if call divert is "
                        "active",
                        "value": 2,
                    },
                    {
                        "description": "Subscriber Specific - use the "
                        "value in the subscriber short "
                        "network name field",
                        "value": 3,
                    },
                ],
                "default": 0,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The source used to populate the Short Network "
                "Name information element",
                "multiline": 0,
                "name": "short_network_name_source",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "integer",
                "unique": 0,
                "verbose_name": "short network name source",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {
                        "description": "Global config - use the "
                        "attribute in this object for all "
                        "subscribers",
                        "value": 0,
                    },
                    {
                        "description": "Subscriber Msisdn - the "
                        "subscribers private cellular "
                        "number",
                        "value": 1,
                    },
                    {
                        "description": "Subscriber Msisdn and Call "
                        "Divert - the subscribers private "
                        "cellular number with an "
                        "indication if call divert is "
                        "active",
                        "value": 2,
                    },
                    {
                        "description": "Subscriber Specific - use the "
                        "value in the subscriber short "
                        "network name field",
                        "value": 3,
                    },
                ],
                "default": 0,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The source used to populate the Long Network "
                "Name information element",
                "multiline": 0,
                "name": "long_network_name_source",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "integer",
                "unique": 0,
                "verbose_name": "long network name source",
            },
        ],
        "name": "plmn",
    }

    data = [
        {
            "cc": "",
            "equivalent_plmns": "",
            "full_network_name": "Dense Air Marlow Lab",
            "hsm_op": 0,
            "id": 1,
            "long_network_name_source": 0,
            "mcc": "001",
            "mnc": "59",
            "ndc": "",
            "op": "RAE$7h14eeI1+JXntam6xzSQncaWRDDpZUWN4WIyBsIDW9SUfdRWN4f3qvL1VWsM1bLa",
            "raemis_id": 0,
            "short_network_name": "DA_Marlow_Lab",
            "short_network_name_source": 0,
            "top": "",
        },
        {
            "cc": "",
            "equivalent_plmns": "",
            "full_network_name": "Dense Air Marlow Lab 00101",
            "hsm_op": 0,
            "id": 2,
            "long_network_name_source": 0,
            "mcc": "001",
            "mnc": "01",
            "ndc": "",
            "op": "RAE$Ti+ahShFJo7CBuIfLXG5ow==",
            "raemis_id": 0,
            "short_network_name": "DA_Marlow_Lab_00101",
            "short_network_name_source": 0,
            "top": "",
        },
    ]

    def test_normalize_plmn_data(self):
        rv = PLMNQuery.normalize_data(self.data[0], self.schema)
        self.assertEqual(rv.get("id"), 1)
        self.assertEqual(rv.get("long_network_name_source"), "Global config")
        self.assertEqual(rv.get("short_network_name_source"), "Global config")


class SGwSessionQueryTests(unittest.TestCase):

    data = [
        {
            "ambr_dl": 1000000,
            "ambr_ul": 1000000,
            "apn": "*.mnc059.mcc001.gprs",
            "bearer_id": 5,
            "creation_date": "",
            "downlink_flow_id": 518,
            "ecgi": "315010:269570",
            "id": 64,
            "imsi": "001590140010806",
            "mbr_dl": 1000000,
            "mbr_ul": 1000000,
            "mgw_flow_group_id": 64,
            "paa_ipv4": "**************",
            "qci": 9,
            "s11_cp_ipv4": "127.0.0.1",
            "s11_teid": 199410526,
            "s5s8_cp_teid": 199410528,
            "s5s8_teid": 199410527,
            "sgw_id": 1,
            "tai": "00159:10",
            "uplink_flow_id": 517,
        },
        {
            "ambr_dl": 100000,
            "ambr_ul": 100000,
            "apn": "ims.mnc059.mcc001.gprs",
            "bearer_id": 6,
            "creation_date": "",
            "downlink_flow_id": 526,
            "ecgi": "315010:269570",
            "id": 65,
            "imsi": "001590140010806",
            "mbr_dl": 100000,
            "mbr_ul": 100000,
            "mgw_flow_group_id": 65,
            "paa_ipv4": "***********",
            "qci": 5,
            "s11_cp_ipv4": "127.0.0.1",
            "s11_teid": 199410526,
            "s5s8_cp_teid": 199410530,
            "s5s8_teid": 199410529,
            "sgw_id": 1,
            "tai": "00159:10",
            "uplink_flow_id": 525,
        },
        {
            "ambr_dl": 1000000,
            "ambr_ul": 1000000,
            "apn": "*.mnc059.mcc001.gprs",
            "bearer_id": 5,
            "creation_date": "",
            "downlink_flow_id": 878,
            "ecgi": "00159:269057",
            "id": 100,
            "imsi": "001590140010801",
            "mbr_dl": 1000000,
            "mbr_ul": 1000000,
            "mgw_flow_group_id": 100,
            "paa_ipv4": "**************",
            "qci": 9,
            "s11_cp_ipv4": "127.0.0.1",
            "s11_teid": 199410668,
            "s5s8_cp_teid": 199410670,
            "s5s8_teid": 199410669,
            "sgw_id": 1,
            "tai": "00159:10",
            "uplink_flow_id": 877,
        },
        {
            "ambr_dl": 1000000,
            "ambr_ul": 1000000,
            "apn": "*.mnc059.mcc001.gprs",
            "bearer_id": 5,
            "creation_date": "",
            "downlink_flow_id": 886,
            "ecgi": "00159:269057",
            "id": 101,
            "imsi": "001590140010966",
            "mbr_dl": 1000000,
            "mbr_ul": 1000000,
            "mgw_flow_group_id": 101,
            "paa_ipv4": "**************",
            "qci": 9,
            "s11_cp_ipv4": "127.0.0.1",
            "s11_teid": 199410672,
            "s5s8_cp_teid": 199410674,
            "s5s8_teid": 199410673,
            "sgw_id": 1,
            "tai": "00159:10",
            "uplink_flow_id": 885,
        },
    ]

    def test_normalize_sgw_session_data(self):
        rv = SGwSessionQuery.normalize_data(self.data[0])
        self.assertEqual(rv.get("id"), 64)
        self.assertEqual(rv.get("bearer_id"), 5)
        self.assertEqual(rv.get("sgw_id"), 1)


class SGwQueryTests(unittest.TestCase):

    data = [
        {
            "admin_state": 2,
            "cdr_failure_handling": 0,
            "cdr_peak_data_sampling_period": 0,
            "cdr_periodicity": 0,
            "creation_date": "",
            "dscp": 0,
            "id": 1,
            "mgw_ctrl_active_url": 1,
            "mgw_ctrl_flow_attr_1": 1,
            "mgw_ctrl_flow_attr_2": 2,
            "mgw_ctrl_flow_attr_3": 3,
            "mgw_ctrl_flow_attr_4": 6,
            "mgw_ctrl_local_port": 8011,
            "mgw_ctrl_net_device": "lo",
            "mgw_ctrl_url1": "127.0.0.1:8013",
            "mgw_ctrl_url2": "",
            "mgw_ctrl_url3": "",
            "name": "SGW",
            "node_id": 0,
            "oper_state": 2,
            "raemis_id": 0,
            "s11_gtpv2_id": 1,
            "s1u_dp_ipv4_enabled": 1,
            "s1u_dp_ipv6_enabled": 1,
            "s1u_ep_group_id": 1,
            "s5s8_ep_group_id": 1,
            "s5s8_gtpv2_id": 1,
            "sctp_read_count": 200,
        }
    ]

    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The naming attribute",
                "multiline": 0,
                "name": "id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "delete"],
                "type": "unsignedint",
                "unique": 1,
                "verbose_name": "ID",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "An optional descriptive name for this network " "function",
                "max_length": 128,
                "multiline": 0,
                "name": "name",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "text",
                "unique": 0,
                "verbose_name": "name",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "locked", "value": 1},
                    {"description": "unlocked", "value": 2},
                    {"description": "shutdown", "value": 3},
                ],
                "default": 2,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The administrative state of the managed object",
                "multiline": 0,
                "name": "admin_state",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "integer",
                "unique": 0,
                "verbose_name": "admin state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The current operational state of the managed " "object",
                "multiline": 0,
                "name": "oper_state",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
        ]
    }

    def test_normalize_sqw_data(self):
        rv = SGwQuery.normalize_data(self.data[0], self.schema)
        self.assertEqual(rv.get("id"), 1)
        self.assertEqual(rv.get("node_id"), 0)
        self.assertEqual(rv.get("admin_state"), "unlocked")
        self.assertEqual(rv.get("oper_state"), "enabled")


class SessionsReportTests(unittest.IsolatedAsyncioTestCase):
    def test_build_report(self):
        data = {
            PLMNQuery: [
                PLMNQuery.normalize_data(i, schema=PLMNQueryTests.schema)
                for i in PLMNQueryTests.data
            ],
            SGwQuery: [
                SGwQuery.normalize_data(i, schema=SGwQueryTests.schema)
                for i in SGwQueryTests.data
            ],
            SGwSessionQuery: [
                SGwSessionQuery.normalize_data(i) for i in SGwSessionQueryTests.data
            ],
        }
        rv = SessionsReport.build_report(data)
        self.assertIsInstance(rv, list)
        self.assertEqual(len(rv), 4)
        for n, item in enumerate(rv):
            with self.subTest(n=n, item=item):
                self.assertIn("sgw", item)
                self.assertIsInstance(item["sgw"], dict)
                self.assertEqual(item["sgw"].get("oper_state"), "enabled")
