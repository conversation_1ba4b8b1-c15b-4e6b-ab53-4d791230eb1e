import datetime
import unittest
import uuid
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import patch

from da_common.models import Status
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.alarms import AlarmsQuery
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.types import AlarmType
from druid_interactions.types import Severity
from druid_interactions.watchdog import Watchdog


class MockPubSub:
    """Mock PubSub class for testing."""

    def __init__(self, config=None, loop=None):
        self.topic = None
        self.project_id = "test-project"
        self.payloads = []
        self.topics = []

    def set_topic(self, topic):
        self.topic = topic
        self.topics.append(topic)

    def push_payload(self, payload):
        self.payloads.append((self.topic, payload))
        return "message-id-" + str(len(self.payloads))

    def model_dump(self):
        return {"topic": self.topic, "project_id": self.project_id}


class TestProcessInstanceIntegrated(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    """Test the process_instance method with minimal mocking."""

    def setUp(self):
        """Set up the test environment."""
        super().setUp()

        # Create a Watchdog instance
        self.watchdog = Watchdog(config=self.config)

        # Create a mock PubSub (only mock external services)
        self.mock_pubsub = MockPubSub(config=self.config)
        self.watchdog.pubsub = self.mock_pubsub

        # Set up logger mock
        self.watchdog.logger = MagicMock()

        # Create a test instance with a unique name
        self.instance = Instance(
            name=f"test-druid-{uuid.uuid4().hex[:8]}",
            host="***********",
            port=443,
            secret_id="test-secret",
            status=Status.UNKNOWN,
        )

        # Sample alarm data for StatusReport
        self.alarm_data = {
            "id": 101,
            "obj_id": 1001,
            "obj_class": "eNodeB",
            "event_type": "TEST_ALARM",
            "severity": "MAJOR",
            "probable_cause": "Test alarm cause",
            "add_text": "Test alarm description",
            "start_time": datetime.datetime.now(
                tz=datetime.timezone.utc
            ),  # Use datetime object, not string
            "acknowledged": False,
            "extra_info": {"identity": 12345},  # Use integer instead of string
        }

        # Sample report data
        self.report_data = {AlarmsQuery: [self.alarm_data]}

        # Create sample alarms
        self.db_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=100,
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="eNodeB/?id=1000",
            event_type="EXISTING_ALARM",
            obj_id=1000,
            severity=Severity.MINOR,
            message="Existing alarm",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        self.new_alarm = AlarmEntry(
            device_id=self.instance.name,
            alarm_id=101,
            alarm_type=AlarmType.INSTANCE_ALARM,
            component="eNodeB/?id=1001",
            event_type="TEST_ALARM",
            obj_id=1001,
            severity=Severity.MAJOR,
            message="Test alarm cause",
            cleared=False,
            event_id=uuid.uuid4(),
        )

        # Mock the clients attribute
        self.watchdog.clients = {}

    async def asyncSetUp(self):
        """Set up async components."""
        # Create the database pool
        self.pool = await PoolFactory.create(self.config)

        # Mock the pool.render method to return PostgreSQL-compatible queries
        original_render = self.pool.render

        def mock_render(query, **values):
            # Convert named parameters to positional parameters
            sql, args = original_render(query, **values)
            # Replace :name with $1, :status with $2, etc.
            param_count = 1
            for key in values:
                sql = sql.replace(f":{key}", f"${param_count}")
                param_count += 1
            return sql, args

        self.pool.render = mock_render
        self.watchdog.pool = self.pool

        # Apply migrations to ensure the database is set up correctly
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                await self.apply_migrations(self.pool, connection)

                # Insert the test instance into the database
                sql, args = self.pool.render(
                    self.instance.sql_insert(), **self.instance._values
                )
                await connection.execute(sql, *args)

    async def asyncTearDown(self):
        """Clean up async resources."""
        if hasattr(self, "pool") and self.pool:
            # Clean up any test data
            async with self.pool.acquire() as connection:
                async with connection.transaction():
                    # Delete test alarms
                    await connection.execute(
                        f"DELETE FROM {AlarmEntry.schema}.alarms WHERE device_id = $1",
                        self.instance.name,
                    )

                    # Delete test instance
                    await connection.execute(
                        f"DELETE FROM {Instance.schema}.instances WHERE name = $1",
                        self.instance.name,
                    )

            # Close the pool
            await self.pool.close()

    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_integrated(
        self, mock_get_credentials, mock_get_report_data
    ):
        """Test the process_instance method with minimal mocking."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock StatusReport.get_report_data
        mock_get_report_data.return_value = self.report_data

        # Mock update_instance_status to avoid SQL execution issues
        original_update_instance_status = self.watchdog.update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Acquire a database connection
        async with self.pool.acquire() as connection:
            # Call the method under test
            await self.watchdog.process_instance(self.instance, connection)

            # Verify PubSub topics were set
            self.assertIn("nms-alarms", self.mock_pubsub.topics)

            # Verify payloads were pushed to PubSub
            self.assertGreaterEqual(len(self.mock_pubsub.payloads), 1)

            # Check if status update was published
            status_updates = [
                p for t, p in self.mock_pubsub.payloads if t == "nms-device-status"
            ]
            self.assertGreaterEqual(len(status_updates), 1)

            # Verify update_instance_status was called
            self.watchdog.update_instance_status.assert_called_once()

            # Restore the original method
            self.watchdog.update_instance_status = original_update_instance_status

    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_exception(
        self, mock_get_credentials, mock_get_report_data
    ):
        """Test process_instance with exception handling in get_report_data."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Mock StatusReport.get_report_data to raise an exception
        mock_get_report_data.side_effect = Exception("Test exception")

        # Mock update_instance_status to avoid SQL execution issues
        original_update_instance_status = self.watchdog.update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Acquire a database connection
        async with self.pool.acquire() as connection:
            # Call the method under test
            await self.watchdog.process_instance(self.instance, connection)

            # Verify an offline alert alarm was created in the database
            sql = f"SELECT * FROM {AlarmEntry.schema}.alarms WHERE device_id = $1 AND alarm_type = $2"
            rows = await connection.fetch(
                sql, self.instance.name, AlarmType.OFFLINE_ALERT.value
            )
            self.assertEqual(len(rows), 1, "An offline alert alarm should be created")
            self.assertEqual(
                rows[0]["severity"], Severity.MAJOR.value, "Alarm severity should be MAJOR"
            )
            self.assertIn(
                "Unable to contact",
                rows[0]["message"],
                "Alarm message should indicate connection issue",
            )

            # Restore the original method
            self.watchdog.update_instance_status = original_update_instance_status

    @patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_process_instance_with_licence_expiry(
        self, mock_get_credentials, mock_get_report_data
    ):
        """Test process_instance with licence expiry handling."""
        # Mock get_credentials
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        # Create sample report data with licence information
        from druid_interactions.queries.features import FeaturesQuery

        licence_data = {
            "expiry_date": datetime.datetime.now(tz=datetime.timezone.utc)
            - datetime.timedelta(days=1),
            # Negative value indicates expired licence (1 day)
            "fields": ["test_field"],
        }
        report_data = {AlarmsQuery: [self.alarm_data], FeaturesQuery: [licence_data]}

        # Mock StatusReport.get_report_data
        mock_get_report_data.return_value = report_data

        # Mock update_instance_status to avoid SQL execution issues
        original_update_instance_status = self.watchdog.update_instance_status
        self.watchdog.update_instance_status = AsyncMock()

        # Set the PubSub topic
        self.mock_pubsub.set_topic("nms-alarms")

        # Acquire a database connection
        async with self.pool.acquire() as connection:
            # Call the method under test
            await self.watchdog.process_instance(self.instance, connection)

            # Verify a licence expiry alarm was created in the database
            sql = f"SELECT * FROM {AlarmEntry.schema}.alarms WHERE device_id = $1 AND alarm_type = $2"
            rows = await connection.fetch(
                sql, self.instance.name, AlarmType.LICENCE_EXPIRY.value
            )
            self.assertEqual(len(rows), 1, "A licence expiry alarm should be created")
            self.assertEqual(
                rows[0]["severity"],
                Severity.CRITICAL.value,
                "Alarm severity should be CRITICAL",
            )
            self.assertIn(
                "expired", rows[0]["message"].lower(), "Alarm message should indicate expiry"
            )

            # Verify the alarm was published to PubSub
            alarm_payloads = [p for t, p in self.mock_pubsub.payloads if t == "nms-alarms"]
            licence_alarms = [p for p in alarm_payloads if "licence" in str(p).lower()]
            self.assertGreaterEqual(
                len(licence_alarms), 1, "A licence alarm should be published"
            )

            # Restore the original method
            self.watchdog.update_instance_status = original_update_instance_status


if __name__ == "__main__":
    unittest.main()
