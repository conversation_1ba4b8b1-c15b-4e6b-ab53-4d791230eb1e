import importlib.resources
import os
import pathlib
import tempfile
import textwrap
import unittest
from collections.abc import Coroutine

import aiosqlite
import asyncpg
import buildpg
from da_common.config import Config
from druid_interactions.pool import OfflineConfig
from druid_interactions.pool import PoolFactory
from druid_interactions.test.test_migrations import TemporaryDBFixture


class RenderTests(unittest.TestCase):
    def test_tuple_value(self):
        kwargs = dict(param=buildpg.Values(version=(0, 1, 0)))
        rv = PoolFactory.values_to_sqlite(kwargs)
        self.assertEqual(rv["param"].values, buildpg.Values(version="(0, 1, 0)").values, rv)

    def test_positional_placeholders(self):
        text = textwrap.dedent(
            """
            INSERT INTO druid_interactions.operation
            (type, uid, prior_uid, node_id, name, token, context,
            resource, priority, parameters, description, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        """
        )
        sql = PoolFactory.build_to_sqlite(text)
        self.assertIn("VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", sql)


class PoolFactoryTests(unittest.IsolatedAsyncioTestCase):
    @unittest.skipUnless(os.getenv("ENVIRONMENT", "").lower() == "gcp", "GCP only")
    async def test_get_gcp_config(self):
        path = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
        coro = PoolFactory.get_config(path, env={"ENVIRONMENT": "gcp"})
        self.assertIsInstance(coro, Coroutine)
        config = await coro
        self.assertIsInstance(config, Config)

    async def test_get_offline_config_postgres(self):
        path = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
        config = await PoolFactory.get_config(path, config_class=OfflineConfig)
        self.assertIsInstance(config, OfflineConfig)
        config.db_config_valid = True
        db_url = config.get_db_url(scheme="postgres")
        self.assertTrue(db_url.startswith("postgres://"), db_url)
        db_name = config.data.get("db", {}).get("database", "")
        self.assertTrue(db_url.endswith(f":5432/{db_name}"), db_url)

    async def test_get_offline_config_sqlite_db(self):
        path = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
        config = await PoolFactory.get_config(path, config_class=OfflineConfig)
        self.assertIsInstance(config, OfflineConfig)
        config.db_config_valid = False
        path = pathlib.Path(tempfile.mkdtemp())
        config.data.get("db", {})["path"] = path
        try:
            db_url = config.get_db_url(scheme="sqlite")
            self.assertTrue(db_url.startswith("file://"), db_url)
            db_name = config.data.get("db", {}).get("database", "")
            self.assertTrue(db_url.endswith(f"/{db_name}.db"), db_url)
        finally:
            path.rmdir()

    async def test_get_offline_config_sqlite_memory(self):
        path = importlib.resources.files("druid_interactions.cfg").joinpath("starter.toml")
        config = await PoolFactory.get_config(path, config_class=OfflineConfig)
        self.assertIsInstance(config, OfflineConfig)
        config.db_config_valid = False
        db_url = config.get_db_url("memory")
        self.assertEqual(db_url, ":memory:")


class ConnectionTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    async def test_table_creation(self):
        try:
            pool = await PoolFactory.create(self.config)
            async with pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(pool, connection)

                    sql, args = pool.render(
                        "SELECT COUNT(*) from druid_interactions.migrations"
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertTrue(rows)
                    if pool.backend is aiosqlite:
                        self.assertEqual(rows[0].get("COUNT(*)"), 1, rows)
                    elif pool.backend is asyncpg:
                        self.assertTrue(dict(rows[0]).get("count"), rows)
        finally:
            await pool.close()
