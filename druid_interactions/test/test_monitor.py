import unittest

import httpx
from asgi_lifespan import LifespanManager
from druid_interactions.main import build_app
from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.monitor import Monitor
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_enodebs import ENodeBReportTests
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.test.test_networks import NetDeviceQueryTests
from fastapi.encoders import jsonable_encoder


class MonitorTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)

    def test_monitor_change(self):
        prior = [1000, 1001, 1004, 1007]
        active = [1000, 1001, 1002, 1005, 1006]
        rv = Monitor.change(prior, active)
        self.assertIsInstance(rv, Monitor.Change)
        self.assertEqual(rv.arrive, {1002, 1005, 1006})
        self.assertEqual(rv.depart, {1004, 1007})

    async def test_monitor_run(self):
        credentials = {
            "username": "system",
            "password": "??????",
        }
        instance = Instance(name="dauk-mrl-green-druid-core", host="*************")
        instance_urls = []

        def fake_response(request):
            url = str(request.url)
            instance_urls.append(url)

            output = {}
            if "enodeb_trx" in url:
                if "schema" in url:
                    output = jsonable_encoder(NetDeviceQueryTests.schema)
                else:
                    output = jsonable_encoder(ENodeBReportTests.enodeb_trxs)
            elif "enodeb" in url:
                if "schema" in url:
                    output = jsonable_encoder(NetDeviceQueryTests.schema)
                else:
                    output = jsonable_encoder(ENodeBReportTests.enodebs)
            return httpx.Response(200, json=output)

        transport = httpx.MockTransport(fake_response)

        async with LifespanManager(self.app) as _lifespan_manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                    await connection.execute("DELETE FROM druid_interactions.surveys")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            with unittest.mock.patch(
                "druid_interactions.creds.get_secret_data", return_value=credentials
            ) as _mock_secret_coro:
                monitor = Monitor(
                    config=self.config,
                    pool=self.app.state.pool,
                    interval=0,
                )
                monitor.clients = {
                    instance.name: httpx.AsyncClient(
                        base_url="https://*************", transport=transport
                    )
                }
                await monitor.run(limit=1)
                self.assertEqual(
                    instance_urls,
                    [
                        "https://*************/enodeb",
                        "https://*************/schema/enodeb",
                        "https://*************/enodeb_trx",
                        "https://*************/schema/enodeb_trx",
                        "https://*************/raemis",
                        "https://*************/schema/raemis",
                    ],
                )

                async with self.app.state.pool.acquire() as connection:
                    query = Survey(device_id=instance.name)
                    sql, args = self.app.state.pool.render(
                        query.sql_select(
                            device_id=query.device_id,
                        ),
                        **query._values,
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertEqual(len(rows), 2, rows)

                await monitor.run(limit=1)
