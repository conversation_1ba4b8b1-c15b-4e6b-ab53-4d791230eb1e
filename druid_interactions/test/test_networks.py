import json
import pathlib
import unittest.mock

from druid_interactions.models.instance import Instance
from druid_interactions.queries.networks import GroupQuery
from druid_interactions.queries.networks import IPRouteQuery
from druid_interactions.queries.networks import IPV4PoolQuery
from druid_interactions.queries.networks import MGWEndpointQuery
from druid_interactions.queries.networks import NetDeviceQuery
from druid_interactions.queries.networks import NetworkSliceQuery
from druid_interactions.queries.networks import NetworksReport
from druid_interactions.queries.networks import PdnQuery
from druid_interactions.queries.networks import SubscriptionProfileQuery


class IPRouteQueryTests(unittest.TestCase):

    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/ip_route.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)

    def test_normalize_ip_route_data(self):
        rv = IPRouteQuery.normalize_data(self.data[0])
        self.assertEqual(rv.get("id"), 4)
        self.assertIsInstance(rv.get("dnm_managed"), bool)
        self.assertFalse(rv.get("dnm_managed"))


class GroupQueryTests(unittest.TestCase):
    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/group.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)


class IPV4PoolQueryTests(unittest.TestCase):
    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/ipv4_pool.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)


class MGWEndpointQueryTests(unittest.TestCase):
    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/mgw_endpoint.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)

    def test_normalize_mgw_endpoint_data(self):
        rv = MGWEndpointQuery.normalize_data(self.data[0])

        self.assertEqual(rv.get("id"), 1)
        self.assertIsInstance(rv.get("subnet_routing_enabled"), bool)


class NetworkSliceQueryTests(unittest.TestCase):
    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/network_slice.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)


class SubscriptionProfileQueryTests(unittest.TestCase):
    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/subscription_profile.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)


class PdnQueryTests(unittest.TestCase):
    test_data_file_path = pathlib.Path(__file__).parent.joinpath(
        "test_data/core/networks/pdn.json"
    )
    with open(test_data_file_path) as f:
        data = json.load(f)


class NetDeviceQueryTests(unittest.TestCase):
    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "help_text": "The naming attribute",
                "name": "id",
                "net_device": 0,
                "primitives": ["get", "create", "delete"],
                "type": "unsignedint",
                "unique": 1,
                "verbose_name": "ID",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "locked", "value": 1},
                    {"description": "unlocked", "value": 2},
                    {"description": "shutdown", "value": 3},
                ],
                "default": 2,
                "help_text": "The administrative state. If locked, this network device is deactivated",
                "name": "admin_state",
                "net_device": 0,
                "type": "integer",
                "unique": 0,
                "verbose_name": "admin state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "unknown", "value": 0},
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "help_text": "The current operational state of the device",
                "name": "oper_state",
                "net_device": 0,
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "none", "value": "none"},
                    {"description": "static", "value": "static"},
                    {"description": "dhcp", "value": "dhcp"},
                    {"description": "unknown", "value": "unknown"},
                ],
                "default": "dhcp",
                "help_text": "The protocol to use for automatic IP configuration.",
                "name": "bootproto",
                "net_device": 0,
                "type": "text",
                "unique": 0,
                "verbose_name": "bootproto",
            },
            {
                "can_be_null": 1,
                "choices": [
                    {"description": "unknown", "value": 0},
                    {"description": "ethernet", "value": 1},
                    {"description": "tun", "value": 16},
                    {"description": "ip6tnl", "value": 25},
                    {"description": "ip6gre", "value": 26},
                ],
                "default": 0,
                "help_text": "interface type e.x. bridge",
                "name": "device_type",
                "net_device": 0,
                "type": "integer",
                "unique": 0,
                "verbose_name": "device type",
            },
        ],
        "name": "net_device",
        "primitives": ["get", "get_csv", "create", "update", "delete", "apply_csv"],
        "unique_together": [["raemis_id", "device", "vlan_id"]],
    }

    data = [
        {
            "admin_state": 2,
            "bootproto": "static",
            "device": "tun_host",
            "device_type": 16,
            "id": 1,
            "ip": "*************",
            "ipv4_forwarding_enabled": 1,
            "ipv6": "",
            "ipv6_forwarding_enabled": 0,
            "mac": "",
            "nat_enabled": 0,
            "netmask": "*************",
            "oper_state": 2,
            "owner": "mgw",
            "parent_device": "",
            "raemis_id": 1,
            "vlan_id": 0,
        },
        {
            "admin_state": 2,
            "bootproto": "static",
            "device": "tun_ims",
            "device_type": 16,
            "id": 2,
            "ip": "**********",
            "ipv4_forwarding_enabled": 1,
            "ipv6": "",
            "ipv6_forwarding_enabled": 0,
            "mac": "",
            "nat_enabled": 0,
            "netmask": "***********",
            "oper_state": 2,
            "owner": "mgw",
            "parent_device": "",
            "raemis_id": 1,
            "vlan_id": 0,
        },
        {
            "admin_state": 2,
            "bootproto": "static",
            "device": "tun_local",
            "device_type": 16,
            "id": 3,
            "ip": "**********",
            "ipv4_forwarding_enabled": 1,
            "ipv6": "",
            "ipv6_forwarding_enabled": 0,
            "mac": "",
            "nat_enabled": 0,
            "netmask": "***********",
            "oper_state": 2,
            "owner": "mgw",
            "parent_device": "",
            "raemis_id": 1,
            "vlan_id": 0,
        },
    ]

    def test_normalize_net_device_data(self):
        rv = NetDeviceQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertEqual(rv.get("id"), 1)

        self.assertIsInstance(rv.get("nat_enabled"), bool)
        self.assertFalse(rv["nat_enabled"])

        self.assertEqual(rv.get("admin_state"), "unlocked")
        self.assertEqual(rv.get("device_type"), "tun")
        self.assertEqual(rv.get("oper_state"), "enabled")


class NetworksReportTests(unittest.IsolatedAsyncioTestCase):
    data = {
        IPRouteQuery: [IPRouteQuery.normalize_data(i) for i in IPRouteQueryTests.data],
        NetDeviceQuery: [
            NetDeviceQuery.normalize_data(i, schema=NetDeviceQueryTests.schema)
            for i in NetDeviceQueryTests.data
        ],
    }

    def test_build_report(self):
        rv = NetworksReport.build_report(self.data)
        self.assertIsInstance(rv, dict)

    async def test_get_report_data(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        report = NetworksReport()
        for typ in (
            IPRouteQuery,
            NetDeviceQuery,
            PdnQuery,
            GroupQuery,
            IPV4PoolQuery,
            NetworkSliceQuery,
            SubscriptionProfileQuery,
            IPV4PoolQuery,
            MGWEndpointQuery,
        ):
            report.queries[typ].get_object = unittest.mock.AsyncMock()
            report.queries[typ].get_object.return_value = unittest.mock.Mock()

        report.queries[
            IPRouteQuery
        ].get_object.return_value.json.return_value = IPRouteQueryTests.data
        report.queries[
            NetDeviceQuery
        ].get_object.return_value.json.return_value = NetDeviceQueryTests.data
        report.queries[PdnQuery].get_object.return_value.json.return_value = PdnQueryTests.data
        report.queries[
            GroupQuery
        ].get_object.return_value.json.return_value = GroupQueryTests.data
        report.queries[
            IPV4PoolQuery
        ].get_object.return_value.json.return_value = IPV4PoolQueryTests.data
        report.queries[
            NetworkSliceQuery
        ].get_object.return_value.json.return_value = NetworkSliceQueryTests.data
        report.queries[
            MGWEndpointQuery
        ].get_object.return_value.json.return_value = MGWEndpointQueryTests.data
        report.queries[
            SubscriptionProfileQuery
        ].get_object.return_value.json.return_value = SubscriptionProfileQueryTests.data

        result = await report.get_report_data(instance)
        self.assertTrue(all(i) for i in result.values())

        report.queries[IPRouteQuery].get_object.assert_called_once()
        report.queries[NetDeviceQuery].get_object.assert_called_once()
