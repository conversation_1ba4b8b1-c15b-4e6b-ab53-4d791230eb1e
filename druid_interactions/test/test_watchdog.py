import collections.abc
import datetime
import json
import unittest
import uuid
from unittest.mock import AsyncMock
from unittest.mock import MagicMock
from unittest.mock import Mock
from unittest.mock import patch

from da_common.models import Status
from dal_pubsub.pubsub import PubSub
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.alarms import AlarmsQuery
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.queries.features import LicenceReport
from druid_interactions.test.test_alarms import AlarmsQueryTests
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.test.test_router_alarms import AlarmsRouterTests
from druid_interactions.types import AlarmType
from druid_interactions.types import LicenceExpiryType
from druid_interactions.types import Severity
from druid_interactions.watchdog import Watchdog
from metrics_collector.api_schema.models import Event
from metrics_collector.api_schema.models import MetricsEvent
from metrics_collector.models import Event as SQLModel_Event


class FeaturesQueryTests(unittest.TestCase):
    schema = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 1},
                    {"description": "enabled", "value": 2},
                ],
                "default": 1,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The current operational state of the managed object",
                "multiline": 0,
                "name": "oper_state",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "integer",
                "unique": 0,
                "verbose_name": "oper state",
            },
        ]
    }

    data = [
        {
            "alarming": 0,
            "allowed_flow_action_types": "",
            "basic_auth_allowed": 1,
            "binding_date": "",
            "bound_seconds": 0,
            "channel_preemption": 1,
            "cs_fallback": 0,
            "demo_license": 0,
            "direct_media": 0,
            "disable_ims": 0,
            "disable_smsc": 0,
            "early_assignment": 0,
            "emergency_data_sessions": 1,
            "enable_5g": 1,
            "enable_5g_nsa": 1,
            "enbgw_max_active_subs": "",
            "enbgw_max_enbs": "",
            "engineering_pack": 1,
            "expiry_date": "",
            "g722": 1,
            "half_rate": 0,
            "iab_enabled": 0,
            "id": 1,
            "interactive_message": 0,
            "interception": 0,
            "issue_date": "2024-04-11 08:29:02",
            "license_id": "faf6089588675046c43a",
            "license_status": 0,
            "licensed_hnb_ids": "",
            "licensed_trx_macs": "",
            "lm_enabled": 0,
            "local_macs": "",
            "local_switched_amr_channels": 0,
            "local_switched_amrwb_channels": 0,
            "mac_mapping": 1,
            "macro_net_abis": 0,
            "macro_net_iuh": 0,
            "macro_net_mf": 0,
            "max_3g_trxs": 0,
            "max_amr_channels": 140,
            "max_amrwb_channels": 140,
            "max_cells": 10,
            "max_data_only_subs": 0,
            "max_data_session_per_cell": 0,
            "max_data_sessions": 0,
            "max_data_subscribers": 10,
            "max_enbs": 10,
            "max_esmlc": "",
            "max_fwa_subs": 0,
            "max_gateway_groups": "",
            "max_gateways_per_group": "",
            "max_gmlc": 0,
            "max_gnbs": "",
            "max_gsmscf": 0,
            "max_gtp_proxy_bearers": "",
            "max_gtp_proxy_dst": "",
            "max_gtp_proxy_routes": "",
            "max_gtp_proxy_sessions": "",
            "max_gtp_proxy_src": "",
            "max_hlr": 0,
            "max_hr_trxs": 0,
            "max_hss": 1,
            "max_iot_subs": 0,
            "max_iot_throughput": 0,
            "max_mgmt_seats": 0,
            "max_mgws": "unlimited",
            "max_nbr_of_4g_aps": 10,
            "max_nbr_of_attached_subs": 0,
            "max_nbr_of_subs": 270,
            "max_nbr_of_trxs": 0,
            "max_pdns": 10,
            "max_rebinds": 0,
            "max_rem_bscs": "unlimited",
            "max_rem_rncs": "unlimited",
            "max_rncs": 0,
            "max_rri_clients": 0,
            "max_s1_clients": "",
            "max_s1_lgws": 0,
            "max_s6a_clients": 0,
            "max_sim_app_subs": 10,
            "max_transcoded_amr_channels": 27,
            "max_transcoded_amrwb_channels": 27,
            "max_ues_per_cell": 0,
            "max_version": 34,
            "max_voice_calls_per_cell": 0,
            "max_wifi_aps": 0,
            "max_wifi_users": 0,
            "messaging_privilege": 0,
            "mocn_gw": 0,
            "msc_vlr": 0,
            "multi_roaming": 0,
            "network_id": "",
            "nms_uri": "",
            "node_type": "",
            "oper_state": 2,
            "org": "",
            "pbx_integration_channels": 0,
            "pbx_integration_provs": 0,
            "pbx_integration_users": 0,
            "perf_stats": 0,
            "product_id": 8,
            "quarantine_mno_mt_sms": 0,
            "radio_scan": 1,
            "radio_zones": 1,
            "realtime_radio_status": 1,
            "redundant_peer_config_master": 0,
            "redundant_peer_id": "",
            "resilience_mode": 0,
            "rtcp_enabled": 1,
            "sccp_api": 0,
            "secgw_enabled": 0,
            "session_auth_allowed": 1,
            "signaalplus_vas": 0,
            "sip_resiliency": 0,
            "sms_api": 1,
            "solution_type": "",
            "static_ips_enabled": 1,
            "supported_until": "2024-12-31 12:00:00",
            "system_id": "1090875381675311761",
            "system_id_type": 0,
            "transcoded_amr_channels": 0,
            "transcoded_amrwb_channels": 0,
            "trx_autoconfig": 1,
            "unlocked_protection": 0,
            "used_amr_channels": 0,
            "used_amrwb_channels": 0,
            "uuid": "",
        }
    ]

    def test_normalize_features_data(self):
        rv = FeaturesQuery.normalize_data(self.data[0], schema=self.schema)

        self.assertIsInstance(rv.get("supported_until"), datetime.datetime)
        self.assertEqual(rv.get("oper_state"), "enabled")

    def test_check_license_status(self):
        now = datetime.datetime.now(tz=datetime.timezone.utc)

        # Test case: more than 30 days - no alarm
        data = dict(expiry_date=now + datetime.timedelta(days=35))
        rv = LicenceReport.check_expiry(data)
        self.assertEqual(rv.get("severity"), Severity.NONE)
        self.assertFalse(rv.get("fields", []))

        # Test case: exactly 30 days - warning
        data = dict(
            expiry_date=(now + datetime.timedelta(days=31)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        )
        rv = LicenceReport.check_expiry(data)
        self.assertEqual(rv.get("severity"), Severity.WARNING)
        self.assertIn("30 days", rv.get("description", "").lower())

        # Test case: 15 days - minor
        data = dict(
            expiry_date=(now + datetime.timedelta(days=16)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        )
        rv = LicenceReport.check_expiry(data)
        self.assertEqual(rv.get("severity"), Severity.MINOR)
        self.assertIn("15 days", rv.get("description", "").lower())

        # Test case: 5 days - major
        data = dict(
            expiry_date=(now + datetime.timedelta(days=6)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        )
        rv = LicenceReport.check_expiry(data)
        self.assertEqual(rv.get("severity"), Severity.MAJOR)
        self.assertIn("5 days", rv.get("description", "").lower())

        # Test case: 0 days - critical
        data = dict(
            expiry_date=(now + datetime.timedelta(days=1)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        )
        rv = LicenceReport.check_expiry(data)
        self.assertEqual(rv.get("severity"), Severity.MAJOR)
        self.assertIn("today", rv.get("description", "").lower())

        # Test case: -1 days (already expired) - critical
        data = dict(
            expiry_date=(now - datetime.timedelta(days=1)).replace(
                hour=0, minute=0, second=0, microsecond=0
            )
        )
        rv = LicenceReport.check_expiry(data)
        self.assertEqual(rv.get("severity"), Severity.CRITICAL)
        self.assertIn("expired", rv.get("description", "").lower())


class AlarmsTests(unittest.TestCase):
    def test_alarm_entries(self):
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        _licence_data = [
            dict(
                start_time=now.isoformat(),
                expiry_date=(now + datetime.timedelta(days=-10)).isoformat(),
            ),
            dict(
                start_time=now.isoformat(),
                expiry_date=(now + datetime.timedelta(days=10)).isoformat(),
            ),
        ]
        report_data = {
            AlarmsQuery: [AlarmsQuery.normalize_data(i) for i in AlarmsQueryTests.data],
        }
        rv = list(Watchdog.alarm_entries(instance, report_data))
        self.assertEqual(len(rv), 4)
        for n, entry in enumerate(rv):
            with self.subTest(n=n, entry=entry):
                self.assertTrue(entry.message)
                self.assertIn(entry.severity, Severity)
                self.assertEqual(entry.alarm_type, AlarmType.INSTANCE_ALARM)
                self.assertIn(
                    entry.component,
                    ("features/?id=1", "net_device/?id=0", "s1_server_enb/?id=19"),
                )
                self.assertIsInstance(entry.obj_id, (int, type(None)))

    def test_reconcile_alarms(self):
        pass


class StatusTests(unittest.TestCase):
    def test_status_no_alarms(self):
        rv = Watchdog.device_status([])
        self.assertEqual(rv, Status.OK)

    def test_status_minor_alarms(self):
        alarms = [AlarmsRouterTests.entries[1]]
        rv = Watchdog.device_status(alarms)
        self.assertEqual(rv, Status.WARNING)

    def test_status_major_alarms(self):
        alarms = [AlarmsRouterTests.entries[0]]
        rv = Watchdog.device_status(alarms)
        self.assertEqual(rv, Status.ERROR)

    def test_status_critical_alarms(self):
        alarms = [AlarmsRouterTests.entries[2]]
        rv = Watchdog.device_status(alarms)
        self.assertEqual(rv, Status.CRITICAL)

    def test_status_alarm_priority(self):
        rv = Watchdog.device_status(AlarmsRouterTests.entries)
        self.assertEqual(rv, Status.CRITICAL)


class PublishingTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        """Set up async components."""
        self.pool = await PoolFactory.create(self.config)
        self.watchdog.pool = self.pool

        async with self.pool.acquire() as connection:
            async with connection.transaction():
                await self.apply_migrations(self.pool, connection)

    def setUp(self):
        """Set up the test environment."""
        # Call the parent setUp to set up the config
        super().setUp()
        # Create a Watchdog instance with mocked dependencies
        self.watchdog = Watchdog(config=self.config)
        self.watchdog.pubsub = MagicMock()
        self.watchdog.logger = MagicMock()

    async def asyncTearDown(self):
        """Clean up async resources."""
        if hasattr(self, "pool") and self.pool:
            await self.pool.close()

    async def test_payload(self):
        async with self.pool.acquire() as connection:
            now = datetime.datetime.now(tz=datetime.timezone.utc)
            entry = AlarmEntry(
                "dauk-mrl-green-druid",
                alarm_type=AlarmType.LICENCE_EXPIRY,
                message=LicenceExpiryType.LICENCE_EXPIRING.value,
                description="Licence will expire in 5 days",
                severity=Severity.MAJOR,
                event_id=uuid.uuid4(),
                created_at=now,
                updated_at=now,
                published_at=now,
            )

            # Insert the alarm into the database
            await self.watchdog.sync_alarm_with_db(entry, connection, "INSERT")

        payload = Watchdog.payload(entry)

        self.assertIsInstance(payload, dict)
        self.assertIn("header", payload)
        self.assertIn("data", payload)
        self.assertEqual(payload["header"].get("eventType"), "alarm")
        self.assertEqual(payload["header"].get("systemDN"), "dauk-mrl-green-druid")
        self.assertEqual(payload["header"].get("reportingEntityName"), "druid_interactions")
        self.assertIn(payload["header"].get("priority"), ["low", "medium", "high"])
        self.assertEqual(
            str(uuid.UUID(payload["header"]["eventId"])),
            payload["header"]["eventId"],
        )
        self.assertEqual(payload["header"]["eventName"], entry.message)

        event_time = payload["header"]["eventTime"]
        self.assertTrue(
            payload["header"]["eventTime"],
        )
        self.assertTrue(event_time.tzinfo, payload)

        self.assertEqual(payload["data"].get("objectType"), "DRUID")
        self.assertTrue(payload["data"].get("perceivedSeverity"))
        self.assertIn("new", payload["data"].get("trendIndication", ""))
        self.assertIn("licence", payload["data"].get("specificProblem", "").lower())
        self.assertTrue(payload["data"].get("type", ""))

        message = json.dumps(payload, cls=PubSub.DateTimeEncoder)
        self.assertTrue(message)

    async def test_alarm_event_matches_metrics_model(self):
        async with self.pool.acquire() as connection:
            entry = AlarmEntry(
                "dauk-mrl-green-druid",
                alarm_type=AlarmType.LICENCE_EXPIRY,
                message=LicenceExpiryType.LICENCE_EXPIRING.value,
                description="Licence will expire in 5 days",
                severity=Severity.MAJOR,
            )
            sql, args = entry.sql_insert()
            await connection.execute(sql, *args)

            # fetch the alarm from the database
            sql, args = self.pool.render(
                entry.sql_select(device_id=entry.device_id),
                **entry._values,
            )  # SELECT * FROM alarms WHERE device_id = %s AND alarm_type = %s
            rows = await connection.fetch(sql, *args)
            alarm = AlarmEntry(**dict(rows[0]))
            payload = Watchdog.payload(alarm)

            event = Event(**payload)
            self.assertTrue(event)
            event_data = event.model_dump(mode="json")
            alarm_event = MetricsEvent(**event_data)
            db_event = SQLModel_Event(**alarm_event.model_dump(mode="json"))
            self.assertTrue(isinstance(db_event, SQLModel_Event))
            self.assertTrue(db_event)

    def test_alarm_entry(self):
        entry = Watchdog.get_alarm_entry(device_id="dauk-mrl-green-druid")
        self.assertIsInstance(entry, AlarmEntry)
        self.assertEqual(entry.alarm_type, AlarmType.WATCHDOG_DEBUG)

        entry = Watchdog.get_alarm_entry(
            device_id="dauk-mrl-green-druid", alarm_type=AlarmType.OFFLINE_ALERT
        )
        self.assertIsInstance(entry, AlarmEntry)
        self.assertIsInstance(entry.alarm_type, AlarmType)
        self.assertEqual(entry.alarm_type, AlarmType.OFFLINE_ALERT)

    async def test_alarm_enrichment(self):
        survey = Survey(
            device_id="dauk-mrl-green-druid-core",
            report_type="ENodeBReport",
            report=json.loads(AlarmsQueryTests.report_json),
        )

        async with self.pool.acquire() as connection:
            async with connection.transaction():
                sql, args = self.pool.render(survey.sql_insert(), **survey.insert_values())
                await connection.execute(sql, *args)

            cells = await Watchdog.get_cells(self.pool)
            self.assertIsInstance(cells, collections.abc.MutableMapping)
            self.assertEqual(len(cells), 15)
            self.assertTrue(all(isinstance(key, int) for key in cells))


class WatchdogTestCase(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    """Test the Watchdog class functionality."""

    async def _setup_test_db(self):
        """Set up a test database with necessary tables."""
        self.pool = await PoolFactory.create(self.config)

        async with self.pool.acquire() as connection:
            async with connection.transaction():
                await self.apply_migrations(self.pool, connection)
                # Clear any existing data
                await connection.execute("DELETE FROM druid_interactions.alarms")
                await connection.execute("DELETE FROM druid_interactions.instances")

                # Insert test instances
                instance_values = [
                    {
                        "name": "test-druid-1",
                        "host": "test-druid-1.example.com",
                        "port": 443,
                        "secret_id": "test-secret-1",
                        "status": Status.OK.value,
                    },
                    {
                        "name": "test-druid-2",
                        "host": "test-druid-2.example.com",
                        "port": 443,
                        "secret_id": "test-secret-2",
                        "status": Status.WARNING.value,
                    },
                ]

                for values in instance_values:
                    instance = Instance(**values)
                    sql, args = self.pool.render(
                        instance.sql_insert(), **instance.insert_values()
                    )
                    await connection.execute(sql, *args)

    def setUp(self):
        """Set up the test environment."""
        super().setUp()

        # Use the existing config from TemporaryDBFixture instead of creating a new one
        # This avoids issues with the config path

        # Sample Druid alarm data (from test_router_alarm)
        self.druid_alarms_data = [
            {
                "id": 28,
                "start_time": "2024-12-02 10:17:45",
                "severity": "critical",
                "obj_class": "enodeb",
                "obj_id": 4,
                "alarm_identifier": "",
                "event_type": "disconnected",
                "probable_cause": "eNodeB disconnected",
                "specific_problem": "",
                "add_text": "The SCTP connection between the eNodeB and the S1 server has been disconnected. eNodeB [00159 4] at&t-marlow-1",
                "internal_id": 57,
                "acknowledged": 0,
            },
            {
                "id": 29,
                "start_time": "2025-02-14 16:25:40",
                "severity": "critical",
                "obj_class": "enodeb",
                "obj_id": 2,
                "alarm_identifier": "",
                "event_type": "disconnected",
                "probable_cause": "eNodeB disconnected",
                "specific_problem": "",
                "add_text": "The SCTP connection between the eNodeB and the S1 server has been disconnected. eNodeB [00159 1053] eNB-1053",
                "internal_id": 83,
                "acknowledged": 0,
            },
            {
                "id": 30,
                "start_time": "2025-03-05 09:38:34",
                "severity": "warning",
                "obj_class": "enodeb",
                "obj_id": 3,
                "alarm_identifier": "",
                "event_type": "",
                "probable_cause": "The name of eNodeB changed.",
                "specific_problem": "",
                "add_text": "The name of eNodeB [00159 1050] changed from at&t-marlow-1050 to green_veNB3",
                "internal_id": 86,
                "acknowledged": 0,
            },
            {
                "id": 31,
                "start_time": "2025-03-05 17:15:52",
                "severity": "critical",
                "obj_class": "enodeb",
                "obj_id": 3,
                "alarm_identifier": "",
                "event_type": "disconnected",
                "probable_cause": "eNodeB disconnected",
                "specific_problem": "",
                "add_text": "The SCTP connection between the eNodeB and the S1 server has been disconnected. eNodeB [00159 1050] green_veNB3",
                "internal_id": 90,
                "acknowledged": 0,
            },
            {
                "id": 272518,
                "start_time": "2025-03-21 12:14:08",
                "severity": "warning",
                "obj_class": "enodeb",
                "obj_id": 7,
                "alarm_identifier": "",
                "event_type": "",
                "probable_cause": "The name of eNodeB changed.",
                "specific_problem": "",
                "add_text": "The name of eNodeB [00159 1051] changed from green_veNB3 to green_veNB",
                "internal_id": 272579,
                "acknowledged": 0,
            },
        ]

        # Create a Watchdog instance with mocked dependencies
        self.watchdog = Watchdog(config=self.config)
        self.watchdog.pubsub = MagicMock()
        self.watchdog.logger = MagicMock()

    async def asyncSetUp(self):
        """Set up async components."""
        await self._setup_test_db()
        self.watchdog.pool = self.pool

    async def asyncTearDown(self):
        """Clean up async resources."""
        if hasattr(self, "pool") and self.pool:
            await self.pool.close()

    async def test_get_instances(self):
        """Test fetching instances from the database."""
        async with self.pool.acquire() as connection:
            instances = await self.watchdog.get_instances(connection)

            self.assertEqual(len(instances), 2)
            self.assertEqual(instances[0].name, "test-druid-1")
            self.assertEqual(instances[1].name, "test-druid-2")

    @patch("druid_interactions.watchdog.StatusReport")
    @patch("druid_interactions.watchdog.get_credentials")
    async def test_get_instance_data_success(self, mock_get_credentials, mock_status_report):
        """Test fetching and processing data for a Druid instance."""
        # Setup mocks
        mock_get_credentials.return_value = {"username": "test", "password": "test"}

        mock_report = AsyncMock()
        mock_report.get_report_data.return_value = {AlarmsQuery: self.druid_alarms_data}
        mock_status_report.return_value = mock_report

        instance = Instance(
            name="test-druid-1",
            host="test-druid-1.example.com",
            port=443,
            secret_id="test-secret",
        )
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                # Execute
                alarms = await self.watchdog.get_alarms_from_druid_instance(
                    instance, connection
                )

                # Verify
                self.assertEqual(len(alarms), 5)
                self.assertEqual(alarms[0].device_id, "test-druid-1")
                self.assertEqual(alarms[0].alarm_type, AlarmType.INSTANCE_ALARM)
                self.assertEqual(alarms[0].alarm_id, 28)

    async def test_get_instance_data_failure(self):
        """Test handling errors when fetching data from a Druid instance."""
        # Setup mocks
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                instance = Instance(
                    name="test-druid-1",
                    host="test-druid-1.example.com",
                    port=443,
                    secret_id="test-secret",
                )
                with unittest.mock.patch(
                    "druid_interactions.watchdog.get_credentials",
                    new=AsyncMock(),
                ):
                    with unittest.mock.patch(
                        "druid_interactions.watchdog.StatusReport.get_report_data",
                        new=AsyncMock(side_effect=Exception("Connection failed")),
                    ):
                        alarms = []

                        try:
                            # Execute
                            alarms = await self.watchdog.get_alarms_from_druid_instance(
                                instance, connection
                            )
                        except Exception as e:
                            self.assertEqual(str(e), "Connection failed")

                        # Verify
                        self.assertEqual(len(alarms), 0)
                        db_alarms = await self.watchdog.get_instance_alarms_from_db(
                            connection, instance
                        )
                        assert len(db_alarms) == 1
                        assert "offline alert" in list(db_alarms.keys())[0]
                        db_alarm = list(db_alarms.values())[0]
                        assert db_alarm.device_id == instance.name
                        assert db_alarm.alarm_type == AlarmType.OFFLINE_ALERT
                        assert db_alarm.message == "Unable to contact Druid instance"
                        assert db_alarm.alarm_id is not None

    async def test_reconcile_new_alarms(self):
        """Test the alarm reconciliation logic."""

        # Setup existing DB alarms
        instance = Instance(name="dauk-mrl-green-druid", host="*************")

        db_alarms = {}

        # New alarms from druid
        new_alarms = list(
            Watchdog.alarm_entries(instance, {AlarmsQuery: self.druid_alarms_data})
        )

        # Execute reconciliation
        reconciled = Watchdog.reconcile_alarms(db_alarms, new_alarms)

        # Verify results
        self.assertEqual(len(reconciled["NEW"]), 5)
        self.assertEqual(reconciled["NEW"][0].alarm_id, 28)

        self.assertEqual(len(reconciled["CLEARED"]), 0)
        self.assertEqual(len(reconciled["UPDATED"]), 0)
        self.assertEqual(len(reconciled["MATCH"]), 0)

    async def test_reconcile_updated_alarms(self):
        def _parse_datetime(dt_str: str | datetime.datetime) -> datetime.datetime:
            """Convert string to datetime if needed."""
            if isinstance(dt_str, datetime.datetime):
                return dt_str
            if isinstance(dt_str, str):
                return datetime.datetime.fromisoformat(dt_str.replace(" ", "T"))
            return datetime.datetime.now(datetime.timezone.utc)

        """Test the alarm reconciliation logic."""
        # Setup existing DB alarms
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        db_alarms = {}
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                for item in self.druid_alarms_data:
                    entry = AlarmEntry(
                        device_id=instance.name,
                        alarm_type=AlarmType.INSTANCE_ALARM,
                        created_at=_parse_datetime(item.get("start_time")),
                        component=f"{item.get('obj_class', '')}/?id={item.get('obj_id', 0)}",
                        event_type=item.get("event_type"),
                        obj_id=item.get("obj_id"),
                        severity=item.get("severity"),
                        message=item.get("probable_cause"),
                        cleared=False,
                        alarm_id=item.get("id"),
                        description=item.get("add_text"),
                        event_id=uuid.uuid4(),
                    )
                    db_alarms[f"{entry.alarm_type}_{entry.alarm_id}"] = entry

                # Generate bulk insert SQL
                values = []
                for entry in list(db_alarms.values()):
                    values.extend(
                        [
                            entry.device_id,
                            entry.alarm_type,
                            entry.created_at,
                            entry.component,
                            entry.event_type,
                            entry.obj_id,
                            entry.severity,
                            entry.message,
                            entry.cleared,
                            entry.alarm_id,
                            entry.description,
                            entry.event_id,
                        ]
                    )

                sql = AlarmEntry.sql_bulk_insert(list(db_alarms.values()))
                # Execute single query
                await connection.execute(sql, *values)

        # New alarms - one updated, one new, one missing
        new_alarms = list(
            Watchdog.alarm_entries(instance, {AlarmsQuery: self.druid_alarms_data})
        )

        # update alarm
        alarm_entry = new_alarms[0]
        alarm_entry.description = "Updated description"

        # Execute reconciliation
        reconciled = Watchdog.reconcile_alarms(db_alarms, new_alarms)

        # Verify results
        self.assertEqual(len(reconciled["UPDATED"]), 1)
        self.assertEqual(reconciled["UPDATED"][0].alarm_id, alarm_entry.alarm_id)
        self.assertEqual(reconciled["UPDATED"][0].description, alarm_entry.description)
        self.assertEqual(len(reconciled["CLEARED"]), 0)
        self.assertEqual(len(reconciled["NEW"]), 0)
        self.assertEqual(len(reconciled["MATCH"]), 4)

    async def test_reconcile_cleared_alarms(self):
        """Test the alarm reconciliation logic."""

        # Setup existing DB alarms
        def _parse_datetime(dt_str: str | datetime.datetime) -> datetime.datetime:
            """Convert string to datetime if needed."""
            if isinstance(dt_str, datetime.datetime):
                return dt_str
            if isinstance(dt_str, str):
                return datetime.datetime.fromisoformat(dt_str.replace(" ", "T"))
            return datetime.datetime.now(datetime.timezone.utc)

        """Test the alarm reconciliation logic."""
        # Setup existing DB alarms
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        db_alarms = {}
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                for item in self.druid_alarms_data:
                    entry = AlarmEntry(
                        device_id=instance.name,
                        alarm_type=AlarmType.INSTANCE_ALARM,
                        created_at=_parse_datetime(item.get("start_time")),
                        component=f"{item.get('obj_class', '')}/?id={item.get('obj_id', 0)}",
                        event_type=item.get("event_type"),
                        obj_id=item.get("obj_id"),
                        severity=item.get("severity"),
                        message=item.get("probable_cause"),
                        cleared=False,
                        alarm_id=item.get("id"),
                        description=item.get("add_text"),
                        event_id=uuid.uuid4(),
                    )
                    db_alarms[f"{entry.alarm_type}_{entry.alarm_id}"] = entry

                # Generate bulk insert SQL
                values = []
                for entry in list(db_alarms.values()):
                    values.extend(
                        [
                            entry.device_id,
                            entry.alarm_type,
                            entry.created_at,
                            entry.component,
                            entry.event_type,
                            entry.obj_id,
                            entry.severity,
                            entry.message,
                            entry.cleared,
                            entry.alarm_id,
                            entry.description,
                            entry.event_id,
                        ]
                    )

                sql = AlarmEntry.sql_bulk_insert(list(db_alarms.values()))
                # Execute single query
                await connection.execute(sql, *values)

        # New alarms - one updated, one new, one missing
        new_alarms = list(
            Watchdog.alarm_entries(instance, {AlarmsQuery: self.druid_alarms_data})
        )

        # remove an alarm from new_alarms
        alarm_entry = new_alarms.pop(0)
        # Execute reconciliation
        reconciled = Watchdog.reconcile_alarms(db_alarms, new_alarms)

        # Verify results
        self.assertEqual(len(reconciled["CLEARED"]), 1)
        self.assertEqual(reconciled["CLEARED"][0].alarm_id, alarm_entry.alarm_id)
        self.assertEqual(reconciled["CLEARED"][0].cleared, True)
        self.assertEqual(len(reconciled["UPDATED"]), 0)
        self.assertEqual(len(reconciled["NEW"]), 0)
        self.assertEqual(len(reconciled["MATCH"]), 4)

    async def test_sync_alarm_with_db(self):
        """Test inserting, updating, and deleting alarms in the database."""

        async with self.pool.acquire() as connection:
            # Test INSERT
            uuid_value = uuid.uuid4()
            new_alarm = AlarmEntry(
                event_id=uuid_value,
                device_id="test-druid-1",
                alarm_type=AlarmType.LICENCE_EXPIRY,
                severity=Severity.MINOR,
                component="test_component",
                alarm_id=105,
                message="initial message",
            )

            await self.watchdog.sync_alarm_with_db(new_alarm, connection, "INSERT")

            # Verify the alarm was inserted
            sql = f"SELECT * FROM {new_alarm.schema}.alarms WHERE event_id = $1"
            rows = await connection.fetch(sql, uuid_value)
            self.assertEqual(len(rows), 1)
            self.assertEqual(rows[0]["device_id"], "test-druid-1")
            self.assertEqual(rows[0]["message"], "initial message")

            # Test UPDATE - using same alarm with modified fields
            new_alarm.severity = Severity.MAJOR
            new_alarm.message = "updated message"
            await self.watchdog.sync_alarm_with_db(new_alarm, connection, "UPDATE")

            # Verify the alarm was updated
            rows = await connection.fetch(sql, uuid_value)
            self.assertEqual(len(rows), 1)
            self.assertEqual(rows[0]["severity"], "major")
            self.assertEqual(rows[0]["message"], "updated message")

            # Test DELETE - using same alarm
            await self.watchdog.sync_alarm_with_db(new_alarm, connection, "DELETE")

            # Verify the alarm was deleted
            rows = await connection.fetch(sql, uuid_value)
            self.assertEqual(len(rows), 0, "Alarm should be deleted")

    async def test_device_status(self):
        """Test determining device status based on alarms."""
        # No alarms = OK
        status = Watchdog.device_status([])
        self.assertEqual(status, Status.OK)

        # Warning alarm = WARNING
        warning_alarm = AlarmEntry(
            event_id=uuid.uuid4(),
            device_id="test-druid-1",
            alarm_type=AlarmType.LICENCE_EXPIRY,
            severity=Severity.MINOR,
        )
        status = Watchdog.device_status([warning_alarm])
        self.assertEqual(status, Status.WARNING)

        # Major alarm = ERROR
        major_alarm = AlarmEntry(
            event_id=uuid.uuid4(),
            device_id="test-druid-1",
            alarm_type=AlarmType.LICENCE_EXPIRY,
            severity=Severity.MAJOR,
        )
        status = Watchdog.device_status([major_alarm])
        self.assertEqual(status, Status.ERROR)

        # Critical alarm = CRITICAL
        critical_alarm = AlarmEntry(
            event_id=uuid.uuid4(),
            device_id="test-druid-1",
            alarm_type=AlarmType.LICENCE_EXPIRY,
            severity=Severity.CRITICAL,
        )
        status = Watchdog.device_status([critical_alarm])
        self.assertEqual(status, Status.CRITICAL)

        # Mixed alarms = worst status
        status = Watchdog.device_status([warning_alarm, major_alarm, critical_alarm])
        self.assertEqual(status, Status.CRITICAL)

    async def test_alarm_payload(self):
        # Test construction of alarm payloads for different alarm states
        # Create a basic alarm
        alarm = AlarmEntry(
            event_id=uuid.uuid4(),
            device_id="test-druid-1",
            alarm_type=AlarmType.OFFLINE_ALERT,
            component="watchdog",
            severity=Severity.MAJOR,
            alarm_id=101,
        )

        async with self.pool.acquire() as connection:
            # Insert the alarm into the database
            await self.watchdog.sync_alarm_with_db(alarm, connection, "INSERT")

        # Test the payload structure
        payload = Watchdog.payload(alarm)
        self.assertIsInstance(payload, dict)
        self.assertIn("header", payload)
        self.assertIn("data", payload)

        # Check header fields
        self.assertEqual(payload["header"]["sourceId"], "101")
        self.assertEqual(payload["header"]["eventType"], "alarm")

        # Check data fields
        self.assertEqual(payload["data"]["objectId"], "test-druid-1")
        self.assertEqual(payload["data"]["objectType"], "DRUID")
        self.assertEqual(payload["data"]["perceivedSeverity"], "major")

        # Test with different severities
        alarm.severity = Severity.CRITICAL
        payload = Watchdog.payload(alarm)
        self.assertEqual(payload["data"]["perceivedSeverity"], "critical")

        alarm.severity = Severity.WARNING
        payload = Watchdog.payload(alarm)
        self.assertEqual(payload["data"]["perceivedSeverity"], "warning")

    async def test_clear_offline_alert_alarm(self):
        async with self.pool.acquire() as connection:
            # Setup existing DB alarms
            instance = Instance(name="dauk-mrl-green-druid", host="*************")
            offline_alert_alarm = Watchdog.generate_offline_alert_alarm_entry(instance=instance)
            sql, args = offline_alert_alarm.sql_insert()
            # Execute single query
            await connection.execute(sql, *args)

            await self.watchdog.clear_offline_alert_alarm(connection, [offline_alert_alarm])
            # Verify the alarm was cleared
            sql = f"SELECT * FROM {offline_alert_alarm.schema}.alarms WHERE event_id = $1"
            rows = await connection.fetch(sql, offline_alert_alarm.event_id)
            self.assertEqual(len(rows), 1)
            self.assertEqual(rows[0]["cleared"], True)

    @unittest.mock.patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @unittest.mock.patch("druid_interactions.watchdog.StatusReport.check_expiry")
    @unittest.mock.patch("druid_interactions.watchdog.get_credentials")
    async def test_licence_expiry_alarm(
        self,
        mock_get_creds,
        mock_check_expiry,
        mock_get_report,
    ):
        """Test updating licence expiry alarm from MINOR to MAJOR severity."""
        # Setup test instance
        instance = Instance(
            name="test-instance", host="localhost", port=8080, secret_id="test-secret"
        )

        # Delete any existing alarms
        async with self.pool.acquire() as connection:
            await connection.execute("DELETE FROM druid_interactions.alarms")

        # Create the initial MINOR alarm directly in DB for testing
        initial_alarm = AlarmEntry(
            device_id=instance.name,
            alarm_type=AlarmType.LICENCE_EXPIRY,
            component="licence",
            severity=Severity.MINOR,
            message=LicenceExpiryType.LICENCE_EXPIRING.value,
            description="Licence will expire in 10 days",
            event_id=uuid.uuid4(),  # Generate a unique ID for testing
        )

        # Insert alarm
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                # Insert the alarm using direct SQL
                columns = [
                    "device_id",
                    "alarm_type",
                    "created_at",
                    "component",
                    "event_id",
                    "severity",
                    "message",
                    "cleared",
                ]
                values = [
                    initial_alarm.device_id,
                    initial_alarm.alarm_type.value,
                    initial_alarm.created_at,
                    initial_alarm.component,
                    initial_alarm.event_id,
                    initial_alarm.severity.value,
                    initial_alarm.message,
                    initial_alarm.cleared,
                ]
                placeholders = [f"${i + 1}" for i in range(len(columns))]

                # Insert using direct SQL
                sql = f"""
                    INSERT INTO druid_interactions.alarms
                    ({", ".join(columns)})
                    VALUES ({", ".join(placeholders)})
                """
                await connection.execute(sql, *values)

                # Verify initial state
                rows = await connection.fetch(
                    "SELECT * FROM druid_interactions.alarms WHERE event_id = $1",
                    initial_alarm.event_id,
                )
                print(f"Initial state: {rows}")
                self.assertEqual(len(rows), 1)
                self.assertEqual(rows[0]["severity"], "minor")

        # Mock for handle_licence_expiry_alarm to use the existing event_id
        mock_get_creds.return_value = {"access_token": "mock-token"}
        mock_get_report.return_value = {FeaturesQuery: [Mock(spec=FeaturesQuery)]}

        # Will be run in handle_licence_expiry_alarm
        mock_check_expiry.return_value = {
            "severity": Severity.MAJOR,
            "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            "description": "Licence will expire in 4 days",
        }

        # Execute test - this should find the existing alarm and update it
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                # Execute the handler
                await self.watchdog.handle_licence_expiry_alarm(connection, instance)

                # Verify the alarm was updated
                rows = await connection.fetch(
                    "SELECT * FROM druid_interactions.alarms WHERE event_id = $1",
                    initial_alarm.event_id,
                )
                self.assertEqual(len(rows), 1, "Should still have 1 alarm")
                self.assertEqual(
                    rows[0]["severity"], "major", "Severity should be updated to MAJOR"
                )
                self.assertEqual(
                    rows[0]["description"],
                    "Licence will expire in 4 days",
                    "Message should be updated",
                )

    @unittest.mock.patch("druid_interactions.watchdog.StatusReport.get_report_data")
    @unittest.mock.patch("druid_interactions.watchdog.StatusReport.check_expiry")
    @unittest.mock.patch("druid_interactions.watchdog.get_credentials")
    async def test_licence_expiry_alarm_do_not_send_pubsub_if_no_change(
        self,
        mock_get_creds,
        mock_check_expiry,
        mock_get_report,
    ):
        """Test not sending pubsub message if alarm is not changed."""
        # Setup test instance
        instance = Instance(
            name="test-instance", host="localhost", port=8080, secret_id="test-secret"
        )

        # Delete any existing alarms
        async with self.pool.acquire() as connection:
            await connection.execute("DELETE FROM druid_interactions.alarms")

        # Create the initial MINOR alarm directly in DB for testing
        initial_alarm = AlarmEntry(
            device_id=instance.name,
            alarm_type=AlarmType.LICENCE_EXPIRY,
            component="licence",
            severity=Severity.WARNING,
            message=LicenceExpiryType.LICENCE_EXPIRING.value,
            description="Licence will expire in 28 days",
            event_id=uuid.uuid4(),  # Generate a unique ID for testing
        )

        # Insert alarm
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                # Insert the alarm using direct SQL
                columns = [
                    "device_id",
                    "alarm_type",
                    "created_at",
                    "component",
                    "event_id",
                    "severity",
                    "message",
                    "cleared",
                    "description",
                ]
                values = [
                    initial_alarm.device_id,
                    initial_alarm.alarm_type.value,
                    initial_alarm.created_at,
                    initial_alarm.component,
                    initial_alarm.event_id,
                    initial_alarm.severity.value,
                    initial_alarm.message,
                    initial_alarm.cleared,
                    initial_alarm.description,
                ]
                placeholders = [f"${i + 1}" for i in range(len(columns))]

                # Insert using direct SQL
                sql = f"""
                    INSERT INTO druid_interactions.alarms
                    ({", ".join(columns)})
                    VALUES ({", ".join(placeholders)})
                """
                await connection.execute(sql, *values)

                # Verify initial state
                rows = await connection.fetch(
                    "SELECT * FROM druid_interactions.alarms WHERE event_id = $1",
                    initial_alarm.event_id,
                )
                print(f"Initial state: {rows}")
                self.assertEqual(len(rows), 1)
                self.assertEqual(rows[0]["severity"], "warning")

        # Mock for handle_licence_expiry_alarm to use the existing event_id
        mock_get_creds.return_value = {"access_token": "mock-token"}
        mock_get_report.return_value = {FeaturesQuery: [Mock(spec=FeaturesQuery)]}

        # Will be run in handle_licence_expiry_alarm
        mock_check_expiry.return_value = {
            "severity": Severity.WARNING,
            "licence_expiry_type": LicenceExpiryType.LICENCE_EXPIRING,
            "description": "Licence will expire in 28 days",
        }

        # Execute test - this should find the existing alarm and update it
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                # Execute the handler
                await self.watchdog.handle_licence_expiry_alarm(connection, instance)

                # Verify the alarm was updated
                rows = await connection.fetch(
                    "SELECT * FROM druid_interactions.alarms WHERE event_id = $1",
                    initial_alarm.event_id,
                )
                self.assertEqual(len(rows), 1, "Should still have 1 alarm")
                self.assertEqual(rows[0]["severity"], "warning", "Severity should not change")
                self.assertEqual(
                    rows[0]["description"],
                    "Licence will expire in 28 days",
                    "Message should not change",
                )
                assert self.watchdog.pubsub.call_count == 0, "Pubsub should not be called"


if __name__ == "__main__":
    unittest.main()
