import unittest.mock

from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.queries.system import SystemQuery
from druid_interactions.queries.system import SystemReport
from inventory_discovery.models.published_types import VersionManifestMessage


class SystemQueryTests(unittest.IsolatedAsyncioTestCase):

    api_object_data = [
        {
            "admin_state": 2,
            "config_master": 0,
            "connected_trx_count": 6,
            "consecutive_failures_to_failover": 9,
            "contactable_trx_count": 6,
            "current_time": "2024:05:10 09:25:02.692",
            "customer_site_id": "",
            "db_integrity_level": 1,
            "db_max_transaction_size": 50,
            "debug_sql": 0,
            "debug_sql_finalize": 0,
            "debug_sql_select": 1,
            "digest": "482f673878a3bb8208b854a875df24796e7adf96",
            "event_log_size": 1000000,
            "expert_http_port": 7000,
            "expert_http_uses_ssl": 0,
            "failover_timer": 30,
            "http_api_addr": "127.0.0.1",
            "http_api_net_device": "lo",
            "http_api_port": 80,
            "http_api_uses_ssl": 0,
            "http_static_dir": "pcn",
            "id": 1,
            "last_db_sync_status": 0,
            "last_db_sync_with_active_peer": "",
            "license_id": "faf6089588675046c43a",
            "log_http_event_details": 1,
            "max_event_id": 100000000,
            "max_event_latency_us": 1000,
            "max_event_subscribers": 250,
            "oper_state": 1,
            "optimise_timers": "10000,1000,4000,2000,6000,5000,604800000",
            "peak_write_load": 0,
            "peer_password": "RAE$5yPJ074ErIGH5N7K5asY6g==",
            "peer_ping_interval": 2,
            "peer_system_id": "",
            "peer_username": "RAE$+xQNkTm6cIAOcOCDttvrfw==",
            "peer_uses_ssl": 1,
            "product_id": "",
            "profile_timers_factor": 0,
            "realtime_monitor_interval": 50,
            "restart_network": 0,
            "restart_required": 0,
            "rtprio_limit": 49,
            "running_timers": 34,
            "scripts_version": "",
            "service_state": 1,
            "software_version": "Raemis Enterprise *******-1, r5d8f0c8d9.",
            "system_id": "1090875381675311761",
            "timer_starts": 8634624,
            "timers_profile": "",
            "update_from_sync": 0,
            "url": "",
            "write_calls": 4519158,
            "write_load": 0,
            "write_total": 4519158,
        }
    ]

    api_schema_data = {
        "attributes": [
            {
                "can_be_null": 0,
                "choices": [
                    {"description": "disabled", "value": 0},
                    {"description": "enabled", "value": 1},
                ],
                "default": 1,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The current operational state of this Raemis "
                "instance and its services",
                "multiline": 0,
                "name": "oper_state",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "integer",
                "verbose_name": "oper state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {
                        "description": "locked - the object is " "administratively locked",
                        "value": 1,
                    },
                    {
                        "description": "unlocked - the object is " "administratively unlocked",
                        "value": 2,
                    },
                    {"description": "shutdown - the object is locking", "value": 3},
                    {"description": "Reset to factory defaults", "value": 4},
                ],
                "default": 2,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The administrative state of the managed "
                "objects. Setting this value to Locked will "
                "restart Raemis. Currently only Locked is "
                "supported.",
                "multiline": 0,
                "name": "admin_state",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "integer",
                "verbose_name": "admin state",
            },
            {
                "can_be_null": 0,
                "choices": [
                    {
                        "description": "Active - this peer is current "
                        "providing active service to "
                        "subscribers",
                        "value": 1,
                    },
                    {
                        "description": "Activating - this peer is "
                        "currently performing the "
                        "failover procedure to begin "
                        "providing active service",
                        "value": 2,
                    },
                    {"description": "Standby - this peer in standby " "mode", "value": 3},
                    {"description": "Unknown - this peer is not " "contactable", "value": 4},
                ],
                "default": 4,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The service state of the RAEMIS peers",
                "multiline": 0,
                "name": "service_state",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "integer",
                "verbose_name": "service state",
            },
            {
                "can_be_null": 0,
                "default": "UNKNOWN",
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The system ID of this RAEMIS instance",
                "max_length": 200,
                "multiline": 0,
                "name": "system_id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "text",
                "verbose_name": "system id",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "The serial number of the issued license.",
                "max_length": 128,
                "multiline": 0,
                "name": "license_id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "text",
                "verbose_name": "license id",
            },
            {
                "can_be_null": 0,
                "default": "UNKNOWN",
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The product ID of this RAEMIS instance",
                "max_length": 200,
                "multiline": 0,
                "name": "product_id",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "text",
                "verbose_name": "product id",
            },
            {
                "can_be_null": 0,
                "default": "UNKNOWN",
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "The RAEMIS software version",
                "max_length": 200,
                "multiline": 0,
                "name": "software_version",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "text",
                "verbose_name": "software version",
            },
            {
                "can_be_null": 0,
                "default": 0,
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "",
                "multiline": 0,
                "name": "restart_required",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get", "create", "update", "delete"],
                "type": "boolean",
                "verbose_name": "restart required",
            },
            {
                "can_be_null": 1,
                "dynamic_update": 1,
                "file_field": 0,
                "help_text": "A valid license key. This field can not be used "
                "with other attributes for the same update.",
                "max_length": 10240,
                "multiline": 0,
                "name": "license",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["create", "update", "delete"],
                "type": "text",
                "verbose_name": "license",
            },
            {
                "can_be_null": 0,
                "default": "UNKNOWN",
                "dynamic_update": 0,
                "file_field": 0,
                "help_text": "Date and time this object was retrieved",
                "max_length": 40,
                "multiline": 0,
                "name": "current_time",
                "net_device": 0,
                "password_display": 0,
                "primitives": ["get"],
                "type": "text",
                "verbose_name": "current time",
            },
        ],
        "display_name_attributes": ["id", "customer_site_id"],
        "dynamic_update": 1,
        "filter_attrs": ["id", "customer_site_id"],
        "name": "raemis",
        "primitives": ["get", "get_csv", "create", "update", "delete", "apply_csv"],
    }

    def test_get_status_from_system_data_with_schema(self):
        query = SystemQuery()
        rv = query.normalize_data(self.api_object_data, self.api_schema_data)
        for attr in (
            "admin_state",
            "current_time",
            "license_id",
            "oper_state",
            "product_id",
            "restart_required",
            "service_state",
            "software_version",
            "id",
            "system_id",
        ):
            with self.subTest(attr=attr):
                value = rv.get(attr, None)
                self.assertIsNotNone(value)
                if attr == "current_time":
                    self.assertTrue(value.endswith("Z"))
                elif attr in ("admin_state", "oper_state", "service_state"):
                    self.assertIn(value.lower(), ("active", "enabled", "unlocked"), type(value))

    async def test_system_report(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")

        report = SystemReport()
        report.queries[SystemQuery].get_object = unittest.mock.AsyncMock()
        report.queries[SystemQuery].get_object.return_value = unittest.mock.Mock()

        report.queries[
            SystemQuery
        ].get_object.return_value.json.return_value = self.api_object_data
        data = await report.get_report_data(instance)
        result = report.build_report(data)
        self.assertIsInstance(result, dict)
        self.assertEqual(["SystemQuery"], list(result))

        survey = Survey(
            device_id=instance.name,
            report_type="SystemQuery",
            report=result,
        )

        items = SystemReport.items(survey.report)
        self.assertIsInstance(items, dict)
        self.assertEqual(len(items), 1)
        self.assertIsInstance(items.get("Raemis Enterprise *******-1, r5d8f0c8d9."), dict)
        item = next(iter(items.values()), {})
        event = next(SystemReport.events(item, manifest_id="dauk-mrl-green-druid"))
        self.assertTrue(event)

        self.assertEqual(event.pop("event_type"), VersionManifestMessage)
        payload = VersionManifestMessage(**event)
        self.assertTrue(payload)

        self.assertEqual(payload.manifest_id, "dauk-mrl-green-druid")
        self.assertEqual(payload.manifest.version, "Raemis Enterprise *******-1")
        self.assertIsNone(payload.manifest.release)
