import datetime
import unittest

import httpx
from asgi_lifespan import Lifespan<PERSON>anager
from da_common.models import Status
from druid_interactions.api import get_current_user
from druid_interactions.main import build_app
from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.pool import pool_manager
from druid_interactions.queries.aspects import AspectsReport
from druid_interactions.queries.enodebs import ENodeBQuery
from druid_interactions.queries.enodebs import ENodeBReport
from druid_interactions.queries.enodebs import ENodeBTrxQuery
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.queries.features import LicenceReport
from druid_interactions.queries.networks import NetworksReport
from druid_interactions.queries.system import SystemQuery
from druid_interactions.reporter import Reporter
from druid_interactions.test.test_aspects import AspectsTests
from druid_interactions.test.test_enodebs import ENodeBReportTests
from druid_interactions.test.test_features import FeaturesTests
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.test.test_networks import NetworksReportTests
from druid_interactions.test.test_router_alarms import AlarmsRouterTests
from druid_interactions.test.test_router_instance import InstanceRouterTests
from druid_interactions.test.test_system import SystemQueryTests
from druid_interactions.types import Role
from druid_interactions.watchdog import Watchdog


class ReportsRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)
        self.app.dependency_overrides[
            get_current_user
        ] = InstanceRouterTests.override_dependency

    async def test_aspects_endpoint_no_alarms(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                async with connection.transaction():
                    instance = Instance(
                        name="dauk-mrl-att-druid-nhe",
                        host="*************",
                        port=443,
                        role=Role.NHE,
                        secret_id="dauk-mrl-att-druid-nhe",
                        status=Status.OK,
                    )
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            reporter = self.app.state.reporter = Reporter(
                self.config, self.app.state.pool, interval=0
            )
            _watchdog = self.app.state.watchdog = Watchdog(
                self.config, self.app.state.pool, interval=0
            )
            reporter.reports["dauk-mrl-att-druid-nhe"] = {
                AspectsReport: AspectsReport().build_report(AspectsTests.data.copy()),
                NetworksReport: NetworksReport().build_report(NetworksReportTests.data.copy()),
            }

            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "nms/druid/reports/dauk-mrl-att-druid-nhe"
                response = await test_client.get(url)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertIsInstance(data, dict)

                self.assertEqual(data.get("device_id"), "dauk-mrl-att-druid-nhe")
                self.assertEqual(data.get("status"), Status.OK)
                self.assertIsInstance(data.get("networks"), dict)
                self.assertIsInstance(data.get("sessions"), list)

                self.assertEqual(
                    datetime.datetime.fromisoformat(data.get("created_at", "")).tzinfo,
                    datetime.timezone.utc,
                )

    async def test_aspects_endpoint_minor_alarms(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                async with connection.transaction():
                    instance = Instance(
                        name="dauk-mrl-att-druid-nhe",
                        host="*************",
                        port=443,
                        role=Role.NHE,
                        secret_id="dauk-mrl-att-druid-nhe",
                        status=Status.WARNING,
                    )
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)
            reporter = self.app.state.reporter = Reporter(
                self.config, self.app.state.pool, interval=0
            )
            watchdog = self.app.state.watchdog = Watchdog(
                self.config, self.app.state.pool, interval=0
            )
            reporter.reports["dauk-mrl-att-druid-nhe"] = {
                AspectsReport: AspectsReport().build_report(AspectsTests.data.copy()),
                NetworksReport: NetworksReport().build_report(NetworksReportTests.data.copy()),
            }
            watchdog.alarms["dauk-mrl-att-druid-nhe"] = {
                AlarmsRouterTests.entries[1].component: AlarmsRouterTests.entries[1]
            }

            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "nms/druid/reports/dauk-mrl-att-druid-nhe"
                response = await test_client.get(url)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertIsInstance(data, dict)

                self.assertEqual(data.get("device_id"), "dauk-mrl-att-druid-nhe")
                self.assertEqual(data.get("status"), Status.WARNING)
                self.assertIsInstance(data.get("networks"), dict)
                self.assertIsInstance(data.get("sessions"), list)

                self.assertEqual(
                    datetime.datetime.fromisoformat(data.get("created_at", "")).tzinfo,
                    datetime.timezone.utc,
                )

    async def test_aspects_endpoint_major_alarms(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                async with connection.transaction():
                    instance = Instance(
                        name="dauk-mrl-att-druid-nhe",
                        host="*************",
                        port=443,
                        role=Role.NHE,
                        secret_id="dauk-mrl-att-druid-nhe",
                        status=Status.ERROR,
                    )
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            reporter = self.app.state.reporter = Reporter(
                self.config, self.app.state.pool, interval=0
            )
            watchdog = self.app.state.watchdog = Watchdog(
                self.config, self.app.state.pool, interval=0
            )
            reporter.reports["dauk-mrl-att-druid-nhe"] = {
                AspectsReport: AspectsReport().build_report(AspectsTests.data.copy()),
                LicenceReport: LicenceReport().build_report(
                    {
                        FeaturesQuery: FeaturesTests.data.copy(),
                        SystemQuery: SystemQueryTests.api_object_data.copy(),
                    }
                ),
                NetworksReport: NetworksReport().build_report(NetworksReportTests.data.copy()),
            }
            watchdog.alarms["dauk-mrl-att-druid-nhe"] = {
                AlarmsRouterTests.entries[0].component: AlarmsRouterTests.entries[0]
            }

            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "nms/druid/reports/dauk-mrl-att-druid-nhe"
                response = await test_client.get(url)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertIsInstance(data, dict)

                self.assertEqual(data.get("device_id"), "dauk-mrl-att-druid-nhe")
                self.assertEqual(data.get("status"), Status.ERROR)
                self.assertIsInstance(data.get("networks"), dict)
                self.assertIsInstance(data.get("sessions"), list)

                self.assertIn("details", data)
                self.assertIsInstance(data["details"].get("features"), dict)
                self.assertIsInstance(data["details"].get("system"), dict)

                self.assertEqual(
                    datetime.datetime.fromisoformat(data.get("created_at", "")).tzinfo,
                    datetime.timezone.utc,
                )

    async def test_report_endpoint_enodebs(self):
        queries = {
            ENodeBQuery: ENodeBReportTests.enodebs,
            ENodeBTrxQuery: ENodeBReportTests.enodeb_trxs,
        }
        report = ENodeBReport.build_report(queries)
        survey = Survey(
            device_id="dauk-mrl-green-druid-core", report_type="ENodeBReport", report=report
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.surveys")

                async with connection.transaction():
                    sql, args = self.app.state.pool.render(
                        survey.sql_insert(), **survey._values
                    )
                    await connection.execute(sql, *args)

                    instance = Instance(
                        name="dauk-mrl-att-druid-nhe",
                        host="*************",
                        status=Status.OK,
                    )
                    sql, args = self.app.state.pool.render(
                        instance.sql_update(name=instance.name), **instance._values
                    )
                    await connection.execute(sql, *args)

            reporter = self.app.state.reporter = Reporter(
                self.config, self.app.state.pool, interval=0
            )
            _watchdog = self.app.state.watchdog = Watchdog(
                self.config, self.app.state.pool, interval=0
            )
            reporter.reports["dauk-mrl-att-druid-nhe"] = {
                AspectsReport: AspectsReport().build_report(AspectsTests.data.copy()),
                NetworksReport: NetworksReport().build_report(NetworksReportTests.data.copy()),
            }

            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "nms/druid/reports/dauk-mrl-att-druid-nhe"
                response = await test_client.get(url)
                self.assertEqual(200, response.status_code, response)
                data = response.json()
                self.assertIsInstance(data, dict)

                self.assertEqual(data.get("device_id"), "dauk-mrl-att-druid-nhe")
                self.assertEqual(data.get("status"), Status.OK)
                self.assertIsInstance(data.get("networks"), dict)
                self.assertIsInstance(data.get("sessions"), list)
                self.assertIsInstance(data.get("enodebs"), list)

                self.assertEqual(
                    datetime.datetime.fromisoformat(data.get("created_at", "")).tzinfo,
                    datetime.timezone.utc,
                )
