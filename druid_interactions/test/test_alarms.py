import datetime
import unittest.mock
import uuid

import httpx
from asgi_lifespan import Lifespan<PERSON>anager
from druid_interactions.main import build_app
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.pool import pool_manager
from druid_interactions.queries.alarms import AlarmsQuery
from druid_interactions.queries.alarms import StatusReport
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.test.test_features import FeaturesTests
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.types import AlarmType
from druid_interactions.types import Severity
from fastapi.encoders import jsonable_encoder


class AlarmsQueryTests(unittest.IsolatedAsyncioTestCase):
    data = [
        {
            "id": 2,
            "start_time": "2024-04-11 08:29:02",
            "severity": "Critical",
            "obj_class": "features",
            "obj_id": 1,
            "alarm_identifier": "248ca06d73ee4e57bb8e9ef1ba7a8ba6",
            "event_type": None,
            "probable_cause": "License is not valid",
            "specific_problem": "",
            "add_text": "",
            "internal_id": 27,
            "acknowledged": 0,
        },
        {
            "acknowledged": 0,
            "add_text": "Warning: Increased maintenance costs, restricted access to "
            "support and latest software releases/patches",
            "alarm_identifier": "",
            "event_type": "",
            "id": 8,
            "internal_id": 8,
            "obj_class": "features",
            "obj_id": 1,
            "probable_cause": "Raemis is approaching end of support",
            "severity": "warning",
            "specific_problem": "",
            "start_time": "2024-05-24 12:51:43",
        },
        {
            "acknowledged": 0,
            "add_text": " - bond0.56 (*************/28) overlaps gre0 (*************/32).",
            "alarm_identifier": "",
            "event_type": "config problem",
            "id": 19,
            "internal_id": 278,
            "obj_class": "net_device",
            "obj_id": 0,
            "probable_cause": "IPv4 subnet overlap",
            "severity": "critical",
            "specific_problem": "Affects gre0, bond0.56.",
            "start_time": "2024-06-10 12:10:21",
        },
        {
            "acknowledged": 0,
            "add_text": "This ENodeB disconnected from the system",
            "alarm_identifier": "",
            "event_type": "",
            "id": 20,
            "internal_id": 474,
            "obj_class": "s1_server_enb",
            "obj_id": 19,
            "probable_cause": "ENodeB disconnected",
            "severity": "critical",
            "specific_problem": "",
            "start_time": "2024-06-11 14:38:30",
        },
    ]

    report_json = """
        {
          "ENodeBQuery": [
            {
              "id": 1,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "plmn_id": "00159",
              "identity": 1052,
              "name": "eNB-1052",
              "sctp_address": "*************:36140",
              "last_inform_time": null
            },
            {
              "id": 2,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "plmn_id": "00159",
              "identity": 1053,
              "name": "eNB-1053",
              "sctp_address": "*************:57896",
              "last_inform_time": null
            },
            {
              "id": 3,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "plmn_id": "00159",
              "identity": 1050,
              "name": "at&t-marlow-1050",
              "sctp_address": "**********:36412",
              "last_inform_time": null
            },
            {
              "id": 4,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "plmn_id": "00159",
              "identity": 4,
              "name": "at&t-marlow-1",
              "sctp_address": "**********:36412",
              "last_inform_time": null
            },
            {
              "id": 6,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "plmn_id": "00159",
              "identity": 263681,
              "name": "AS1030 Cetin NQT",
              "sctp_address": "***************:36412",
              "last_inform_time": null
            },
            {
              "id": 7,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "plmn_id": "00159",
              "identity": 1051,
              "name": "green_veNB",
              "sctp_address": "***********:36412",
              "last_inform_time": null
            }
          ],
          "ENodeBTrxQuery": [
            {
              "id": 1,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269334,
              "enb_id": 1
            },
            {
              "id": 2,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269570,
              "enb_id": 2
            },
            {
              "id": 3,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269336,
              "enb_id": 1
            },
            {
              "id": 4,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269502,
              "enb_id": 1
            },
            {
              "id": 5,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269501,
              "enb_id": 1
            },
            {
              "id": 6,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269333,
              "enb_id": 1
            },
            {
              "id": 7,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 0,
              "cell_id": 268800,
              "enb_id": 3
            },
            {
              "id": 8,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 1,
              "cell_id": 1050,
              "enb_id": 4
            },
            {
              "id": 10,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 67502337,
              "enb_id": 6
            },
            {
              "id": 11,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269335,
              "enb_id": 1
            },
            {
              "id": 13,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269338,
              "enb_id": 1
            },
            {
              "id": 14,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269057,
              "enb_id": 7
            },
            {
              "id": 15,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269058,
              "enb_id": 7
            },
            {
              "id": 16,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269312,
              "enb_id": 1
            },
            {
              "id": 17,
              "oper_state": "enabled",
              "admin_state": "unlocked",
              "name": "",
              "tac": 10,
              "cell_id": 269056,
              "enb_id": 7
            }
          ]
        }
    """

    def test_normalize_data(self):
        rv = AlarmsQuery.normalize_data(self.data[0])
        self.assertEqual(rv.get("obj_class"), "features")
        self.assertEqual(rv.get("obj_id"), 1)
        self.assertIsInstance(rv.get("start_time"), datetime.datetime)
        self.assertLessEqual(
            {
                "event_type",
                "severity",
                "alarm_identifier",
                "probable_cause",
                "specific_problem",
                "add_text",
                "acknowledged",
            },
            set(rv),
        )

    async def test_check_expiry(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        urls = []

        def fake_response(request):
            urls.append(str(request.url))
            output = jsonable_encoder(self.report_json)
            return httpx.Response(200, json=output)

        transport = httpx.MockTransport(fake_response)
        report = StatusReport()
        report.queries[FeaturesQuery].get_object = unittest.mock.AsyncMock()
        report.queries[FeaturesQuery].get_object.return_value = unittest.mock.Mock()

        report.queries[
            FeaturesQuery
        ].get_object.return_value.json.return_value = FeaturesTests.data[0]
        report.queries[AlarmsQuery].get_object = unittest.mock.AsyncMock()
        report.queries[AlarmsQuery].get_object.return_value = unittest.mock.Mock()
        report.queries[AlarmsQuery].get_object.return_value.json.return_value = self.data
        report.queries[AlarmsQuery].client = unittest.mock.Mock()
        report.queries[AlarmsQuery].client.return_value = httpx.AsyncClient(
            base_url=instance.url, transport=transport
        )

        data = await report.get_report_data(instance)
        result = report.build_report(data)
        expiry = report.check_expiry(result[FeaturesQuery])
        self.assertIsInstance(expiry, dict)
        self.assertEqual({"severity", "description", "licence_expiry_type"}, set(expiry))

        report.queries[FeaturesQuery].get_object.assert_called_once()
        report.queries[AlarmsQuery].get_object.assert_called_once()
        self.assertIn("https://*************/api/s1_server_enb?id=19", urls)


class DBTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)

    async def test_insert_alarm_constraint(self):
        async with LifespanManager(self.app):
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.alarms")

                tr = connection.transaction()
                await tr.start()
                entry_1 = AlarmEntry(
                    device_id="dauk-mrl-green-druid",
                    alarm_type=AlarmType.INSTANCE_ALARM,
                    component="s1_server_enb",
                    severity=Severity.CRITICAL,
                    alarm_id=102,
                    event_id=uuid.uuid4(),
                )

                duplicate_entry_2 = AlarmEntry(
                    device_id="dauk-mrl-green-druid",
                    alarm_type=AlarmType.INSTANCE_ALARM,
                    component="s1_server_enb_updated",
                    severity=Severity.CRITICAL,
                    alarm_id=102,
                    event_id=uuid.uuid4(),
                    description="updated description",
                )

                # Insert first entry
                sql, args = entry_1.sql_insert()
                await connection.execute(sql, *args)
                await tr.commit()

                # Verify it was inserted
                sql, args = self.app.state.pool.render(entry_1.sql_select(), **entry_1._values)
                rows = await connection.fetch(sql, *args)
                self.assertEqual(len(rows), 1)
                entries = [AlarmEntry(**dict(row)) for row in rows]
                self.assertEqual(entries[0].device_id, "dauk-mrl-green-druid")
                self.assertEqual(entries[0].component, "s1_server_enb")

                tr = connection.transaction()
                await tr.start()
                # Insert duplicate entry which should update rather than insert
                sql, args = duplicate_entry_2.sql_insert()
                await connection.execute(sql, *args)
                await tr.commit()

                # Verify the update worked by checking component was updated
                query = """
                    SELECT * FROM druid_interactions.alarms
                """
                sql, args = self.app.state.pool.render(
                    query,
                )
                rows = await connection.fetch(sql, *args)
                self.assertEqual(len(rows), 1, "Should still be only one row after upsert")
                self.assertEqual(
                    rows[0]["description"],
                    "updated description",
                    "description should be updated by the upsert",
                )

    async def test_delete_alarm(self):
        async with LifespanManager(self.app):
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.alarms")

                async with connection.transaction():
                    entry = AlarmEntry(
                        device_id="dauk-mrl-green-druid",
                        alarm_type=AlarmType.INSTANCE_ALARM,
                        component="s1_server_enb",
                        severity=Severity.CRITICAL,
                    )
                    sql, args = entry.sql_insert()
                    await connection.execute(sql, *args)

                    sql, args = self.app.state.pool.render(
                        entry.sql_delete(event_id=entry.event_id),
                        **entry._values,
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertTrue(rows)
                    entries = [AlarmEntry(**dict(row)) for row in rows]
                    self.assertEqual(entries[0].created_at, entry.created_at)

                    sql, args = self.app.state.pool.render(entry.sql_select(), **entry._values)
                    rows = await connection.fetch(sql, *args)
                    self.assertFalse(rows)

    async def test_filter_out_enb_alarms(self):
        async with LifespanManager(self.app):
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.alarms")

                async with connection.transaction():
                    entries = [
                        AlarmEntry(
                            device_id="dauk-mrl-green-druid",
                            alarm_type=AlarmType.INSTANCE_ALARM,
                            component="enodeb/?id=3",
                            severity=Severity.CRITICAL,
                        ),
                        AlarmEntry(
                            device_id="dauk-mrl-green-druid",
                            alarm_type=AlarmType.INSTANCE_ALARM,
                            component="enodeb/?id=4",
                            severity=Severity.CRITICAL,
                        ),
                        AlarmEntry(
                            device_id="dauk-mrl-green-druid",
                            alarm_type=AlarmType.INSTANCE_ALARM,
                            component="features/?id=1",
                            severity=Severity.CRITICAL,
                        ),
                    ]
                    # Insert all entries
                    for entry in entries:
                        sql, args = entry.sql_insert()
                        await connection.execute(sql, *args)

                    # Query for non-enodeb entries
                    sql, args = self.app.state.pool.render(
                        f"{entries[0].sql_select(device_id=entries[0].device_id)} AND component NOT LIKE '%enodeb%'",
                        **entries[0]._values,
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertEqual(len(rows), 1)
                    self.assertEqual(
                        rows[0]["component"],
                        "features/?id=1",
                    )
