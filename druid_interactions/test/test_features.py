import datetime
import unittest

from druid_interactions.queries.features import FeaturesQuery


class FeaturesTests(unittest.TestCase):
    data = [
        {
            "alarming": 0,
            "basic_auth_allowed": 1,
            "binding_date": "",
            "bound_seconds": 0,
            "channel_preemption": 1,
            "cs_fallback": 0,
            "demo_license": 0,
            "direct_media": 0,
            "disable_ims": 0,
            "disable_smsc": 0,
            "early_assignment": 0,
            "emergency_data_sessions": 1,
            "enable_5g": 1,
            "enable_5g_nsa": 1,
            "enbgw_max_active_subs": "",
            "enbgw_max_enbs": "",
            "engineering_pack": 1,
            "expiry_date": "2025-04-26 12:00:00",
            "g722": 1,
            "half_rate": 0,
            "iab_enabled": 0,
            "id": 1,
            "interactive_message": 0,
            "interception": 0,
            "issue_date": "2024-04-11 08:29:02",
            "license_id": "faf6089588675046c43a",
            "license_status": 0,
            "licensed_hnb_ids": "",
            "licensed_trx_macs": "",
            "lm_enabled": 0,
            "local_macs": "",
            "local_switched_amr_channels": 0,
            "local_switched_amrwb_channels": 0,
            "mac_mapping": 1,
            "macro_net_abis": 0,
            "macro_net_iuh": 0,
            "macro_net_mf": 0,
            "max_3g_trxs": 0,
            "max_amr_channels": 140,
            "max_amrwb_channels": 140,
            "max_cells": 10,
            "max_data_only_subs": 0,
            "max_data_session_per_cell": 0,
            "max_data_sessions": 0,
            "max_data_subscribers": 10,
            "max_enbs": 10,
            "max_esmlc": "",
            "max_fwa_subs": 0,
            "max_gmlc": 0,
            "max_gnbs": "",
            "max_gsmscf": 0,
            "max_gtp_proxy_bearers": "",
            "max_gtp_proxy_dst": "",
            "max_gtp_proxy_routes": "",
            "max_gtp_proxy_sessions": "",
            "max_gtp_proxy_src": "",
            "max_hlr": 0,
            "max_hr_trxs": 0,
            "max_hss": 1,
            "max_iot_subs": 0,
            "max_iot_throughput": 0,
            "max_mgmt_seats": 0,
            "max_mgws": "unlimited",
            "max_nbr_of_4g_aps": 10,
            "max_nbr_of_attached_subs": 0,
            "max_nbr_of_subs": 270,
            "max_nbr_of_trxs": 0,
            "max_pdns": 10,
            "max_rebinds": 0,
            "max_rem_bscs": "unlimited",
            "max_rem_rncs": "unlimited",
            "max_rncs": 0,
            "max_rri_clients": 0,
            "max_s1_clients": "",
            "max_s1_lgws": 0,
            "max_s6a_clients": 0,
            "max_sim_app_subs": 10,
            "max_transcoded_amr_channels": 27,
            "max_transcoded_amrwb_channels": 27,
            "max_ues_per_cell": 0,
            "max_version": 34,
            "max_voice_calls_per_cell": 0,
            "max_wifi_aps": 0,
            "max_wifi_users": 0,
            "messaging_privilege": 0,
            "mocn_gw": 0,
            "msc_vlr": 0,
            "multi_roaming": 0,
            "network_id": "",
            "nms_uri": "",
            "node_type": "",
            "oper_state": 2,
            "org": "",
            "pbx_integration_channels": 0,
            "pbx_integration_provs": 0,
            "pbx_integration_users": 0,
            "perf_stats": 0,
            "product_id": 8,
            "quarantine_mno_mt_sms": 0,
            "radio_scan": 1,
            "radio_zones": 1,
            "realtime_radio_status": 1,
            "redundant_peer_config_master": 0,
            "redundant_peer_id": "",
            "resilience_mode": 0,
            "rtcp_enabled": 1,
            "sccp_api": 0,
            "secgw_enabled": 0,
            "session_auth_allowed": 1,
            "signaalplus_vas": 0,
            "sip_resiliency": 0,
            "sms_api": 1,
            "solution_type": "",
            "static_ips_enabled": 1,
            "supported_until": "2024-12-31 12:00:00",
            "system_id": "1090875381675311761",
            "system_id_type": 0,
            "transcoded_amr_channels": 0,
            "transcoded_amrwb_channels": 0,
            "trx_autoconfig": 1,
            "unlocked_protection": 0,
            "used_amr_channels": 0,
            "used_amrwb_channels": 0,
            "uuid": "",
        }
    ]

    def test_get_licence_from_features_data(self):
        rv = FeaturesQuery.normalize_data(self.data[0])
        self.assertTrue(rv.get("system_id"))
        for attr in (
            "issue_date",
            "expiry_date",
            "binding_date",
            "supported_until",
        ):
            with self.subTest(attr=attr):
                self.assertIn(attr, rv)
                value = rv[attr]

                if value is not None:
                    self.assertIsInstance(value, datetime.datetime)
                    self.assertEqual(value.tzinfo, datetime.timezone.utc)
