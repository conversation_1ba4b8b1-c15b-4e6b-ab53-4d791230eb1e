import unittest

import httpx
from asgi_lifespan import Lifespan<PERSON>anager
from druid_interactions.main import build_app
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_migrations import TemporaryDBFixture


class HealthRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    async def test_health_endpoint(self):
        app = build_app(
            self.config,
            lifespan=pool_manager,
        )
        # self.app.state.clients = self.config.build_clients()
        # self.app.dependency_overrides[get_current_user] = override_dependency
        async with LifespanManager(app) as manager:
            transport = httpx.ASGITransport(app=manager.app)
            async with httpx.AsyncClient(
                base_url="http://localhost/",
                transport=transport,
            ) as test_client:
                url = "/health"
                response = await test_client.get(url)
                self.assertEqual(200, response.status_code)
                data = response.json()
                self.assertEqual(data.get("app"), "druid_interactions")
