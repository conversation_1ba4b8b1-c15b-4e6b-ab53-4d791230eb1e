import functools
import unittest.mock
import urllib.parse

import httpx
from asgi_lifespan import LifespanManager
from druid_interactions.api import get_current_user
from druid_interactions.main import build_app
from druid_interactions.models.instance import Instance
from druid_interactions.pool import pool_manager
from druid_interactions.test.test_migrations import TemporaryDBFixture
from druid_interactions.test.test_router_instance import InstanceRouterTests
from druid_interactions.test.test_system import SystemQueryTests
from fastapi.encoders import jsonable_encoder


class SystemRouterTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)
        self.app.dependency_overrides[
            get_current_user
        ] = InstanceRouterTests.override_dependency

    async def test_status_endpoint_instance_gone(self):
        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")

            transport = httpx.ASGITransport(app=manager.app)

            async with httpx.AsyncClient(
                base_url="http://localhost/", transport=transport
            ) as client:

                url = "nms/druid/dauk-mrl-green-druid/system/status"
                response = await client.get(url)
                self.assertEqual(410, response.status_code, response.json())
                data = response.json()
                self.assertIsInstance(data, dict)
                self.assertTrue(data)

    async def test_status_endpoint(self):
        credentials = {
            "username": "system",
            "password": "??????",
        }
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        instance_urls = []

        def fake_response(request):
            url = str(request.url)
            instance_urls.append(url)
            if "schema" in url:
                output = jsonable_encoder(SystemQueryTests.api_schema_data)
            else:
                output = jsonable_encoder(SystemQueryTests.api_object_data)
            return httpx.Response(200, json=output)

        transport = httpx.MockTransport(fake_response)
        self.app.state.clients[instance.name] = httpx.AsyncClient(
            base_url=instance.url, transport=transport
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            with unittest.mock.patch(
                "druid_interactions.creds.get_secret_data", return_value=credentials
            ) as mock_secret_coro:
                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/", transport=transport
                ) as client:

                    url = f"nms/druid/{instance.name}/system/status"
                    response = await client.get(url)

                    mock_secret_coro.assert_called_once()
                    self.assertEqual(len(instance_urls), 2)
                    for n, url in enumerate(instance_urls):
                        with self.subTest(n=n, url=url):
                            self.assertTrue(
                                urllib.parse.urlparse(url).netloc.startswith("*************")
                            )
                            if n == 0:
                                self.assertEqual(urllib.parse.urlparse(url).path, "/api/raemis")
                            else:
                                self.assertEqual(
                                    urllib.parse.urlparse(url).path, "/api/schema/raemis"
                                )

                    data = response.json()
                    self.assertIsInstance(data, dict)
                    self.assertTrue(data)

                    for attr in (
                        "admin_state",
                        "current_time",
                        "license_id",
                        "oper_state",
                        "product_id",
                        "restart_required",
                        "service_state",
                        "software_version",
                        "system_id",
                    ):
                        with self.subTest(attr=attr):
                            value = data.get(attr, None)
                            self.assertIsNotNone(value)
                            if attr == "current_time":
                                self.assertTrue(value.endswith("Z"))
                            elif attr in ("admin_state", "oper_state", "service_state"):
                                self.assertIn(
                                    value.lower(),
                                    ("active", "enabled", "unlocked"),
                                    type(value),
                                )
                    self.assertEqual(200, response.status_code, response.json())

    async def test_post_raemis_endpoint(self):
        credentials = {
            "username": "system",
            "password": "??????",
        }
        instance = Instance(name="dauk-mrl-green-druid", host="*************")
        outputs = [
            httpx.Response(200, json=dict(success=True)),
            httpx.Response(400, json=dict(success=False)),
        ]
        witness = {}

        def fake_response(request, outputs=[]):
            witness["url"] = str(request.url)
            witness["method"] = request.method
            rv = outputs.pop(0)
            return rv

        transport = httpx.MockTransport(
            functools.partial(fake_response, outputs=outputs.copy())
        )
        self.app.state.clients[instance.name] = httpx.AsyncClient(
            base_url=instance.url, transport=transport
        )

        async with LifespanManager(self.app) as manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.instances")
                    sql, args = self.app.state.pool.render(
                        instance.sql_insert(), **instance._values
                    )
                    await connection.execute(sql, *args)

            with unittest.mock.patch(
                "druid_interactions.creds.get_secret_data", return_value=credentials
            ) as _mock_secret_coro:
                transport = httpx.ASGITransport(app=manager.app)
                async with httpx.AsyncClient(
                    base_url="http://localhost/", transport=transport
                ) as client:

                    for n, output in enumerate(outputs):
                        with self.subTest(n=n, output=output):
                            data = {
                                "id": 1,
                                "admin_state": 1,
                            }
                            url = "nms/druid/dauk-mrl-green-druid/system/raemis"
                            payload = jsonable_encoder(data)
                            response = await client.post(url, json=payload)

                            self.assertEqual(output.status_code, response.status_code)

                            data = response.json()
                            if n:
                                self.assertEqual(
                                    data.get("detail"), output.content.decode("utf8")
                                )
                            else:
                                self.assertIn("success", data.get("detail"))

                            self.assertEqual(witness.get("method", "").upper(), "PATCH")
                            self.assertIn("/api/raemis?id=1", witness.get("url", ""))
