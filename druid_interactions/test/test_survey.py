import datetime
import json
import unittest
import uuid

from asgi_lifespan import LifespanManager
from druid_interactions.main import build_app
from druid_interactions.models.survey import Survey
from druid_interactions.pool import pool_manager
from druid_interactions.queries.enodebs import ENodeBQuery
from druid_interactions.queries.enodebs import ENodeBReport
from druid_interactions.queries.enodebs import ENodeBTrxQuery
from druid_interactions.test.test_enodebs import ENodeBReportTests
from druid_interactions.test.test_migrations import TemporaryDBFixture


class SurveyTests(TemporaryDBFixture, unittest.IsolatedAsyncioTestCase):
    def setUp(self):
        super().setUp()
        self.app = build_app(self.config, lifespan=pool_manager)

    def test_report_encoding(self):
        queries = {
            ENodeBQuery: ENodeBReportTests.enodebs,
            ENodeBTrxQuery: ENodeBReportTests.enodeb_trxs,
        }
        report = ENodeBReport.build_report(queries)
        survey = Survey(
            device_id="dauk-mrl-green-druid-core", report_type="ENodeBReport", report=report
        )
        self.assertIsInstance(survey.survey_id, uuid.UUID)
        content = json.dumps(survey.report)
        self.assertIsInstance(content, str)

    async def test_insert_survey_constraint(self):
        _queries = {
            ENodeBQuery: ENodeBReportTests.enodebs,
            ENodeBTrxQuery: ENodeBReportTests.enodeb_trxs,
        }
        async with LifespanManager(self.app) as _lifespan_manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.surveys")

                async with connection.transaction():
                    then = datetime.datetime.now(tz=datetime.timezone.utc)
                    now = then + datetime.timedelta(seconds=10)
                    surveys = [
                        Survey(
                            device_id="dauk-mrl-green-druid-core",
                            report_type="ENodeBReport",
                            report=dict(ENodeBQuery=[{"status": "OK"}]),
                            updated_at=then,
                        ),
                        Survey(
                            device_id="dauk-mrl-green-druid-core",
                            report_type="ENodeBReport",
                            report=dict(ENodeBQuery=[{"status": "OK"}]),
                            updated_at=now,
                        ),
                    ]
                    sql, args = self.app.state.pool.render(
                        surveys[0].sql_insert(), **surveys[0]._values
                    )
                    await connection.execute(sql, *args)

                    sql, args = self.app.state.pool.render(
                        surveys[0].sql_select(
                            device_id=surveys[0].device_id,
                            report_type=surveys[0].report_type,
                        ),
                        **surveys[0]._values,
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertEqual(len(rows), 1)
                    self.assertEqual(rows[0]["survey_id"], surveys[0].survey_id)

                    sql, args = self.app.state.pool.render(
                        surveys[1].sql_insert(), **surveys[1]._values
                    )
                    await connection.execute(sql, *args)

                    sql, args = self.app.state.pool.render(
                        surveys[1].sql_select(
                            device_id=surveys[1].device_id,
                            report_type=surveys[1].report_type,
                        ),
                        **surveys[1]._values,
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertEqual(len(rows), 1)
                    self.assertEqual(rows[0]["survey_id"], surveys[1].survey_id)
                    self.assertEqual(rows[0]["updated_at"], now)

    async def test_delete_survey(self):
        async with LifespanManager(self.app) as _lifespan_manager:
            async with self.app.state.pool.acquire() as connection:
                async with connection.transaction():
                    await self.apply_migrations(self.app.state.pool, connection)
                async with connection.transaction():
                    await connection.execute("DELETE FROM druid_interactions.surveys")

                async with connection.transaction():
                    survey = Survey(
                        device_id="dauk-mrl-green-druid-core",
                    )
                    sql, args = self.app.state.pool.render(
                        survey.sql_insert(), **survey._values
                    )
                    await connection.execute(sql, *args)

                    sql, args = self.app.state.pool.render(
                        survey.sql_delete(survey_id=survey.survey_id), **survey._values
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertTrue(rows)
                    surveys = [Survey(**dict(row)) for row in rows]
                    self.assertEqual(surveys[0].updated_at, survey.updated_at)

                    sql, args = self.app.state.pool.render(
                        survey.sql_select(), **survey._values
                    )
                    rows = await connection.fetch(sql, *args)
                    self.assertFalse(rows)
