[{"id": 1, "apn": "*", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": 1450, "ipv4_pool_id": 1, "ep_group_id": 2, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=1"}, {"id": 2, "apn": "ims", "primary_dns": "", "secondary_dns": "", "ue_mtu": 1392, "ipv4_pool_id": 2, "ep_group_id": 3, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=2"}, {"id": 3, "apn": "emergency", "primary_dns": "", "secondary_dns": "", "ue_mtu": 1392, "ipv4_pool_id": 2, "ep_group_id": 3, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=3"}, {"id": 4, "apn": "vzwims", "primary_dns": "", "secondary_dns": "", "ue_mtu": 1392, "ipv4_pool_id": 2, "ep_group_id": 3, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=4"}, {"id": 6, "apn": "r1.denseair", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": 1392, "ipv4_pool_id": 0, "ep_group_id": 2, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=6"}, {"id": 8, "apn": "eurocom", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": 1392, "ipv4_pool_id": 1, "ep_group_id": 2, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=8"}, {"id": 9, "apn": "internet.00101", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": 1392, "ipv4_pool_id": 1, "ep_group_id": 2, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=9"}, {"id": 10, "apn": "inet.mnc059.mcc001.gprs", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": 1392, "ipv4_pool_id": 1, "ep_group_id": 2, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=10"}, {"id": 11, "apn": "radisys", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": 1392, "ipv4_pool_id": 0, "ep_group_id": 2, "allow_multiple_connections": 0, "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": 0, "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": 20, "use_mac_mappings": 0, "raemis_id": 0, "sticky_ip_assignment": 0, "ipv4pools": "/api/pdn/ipv4pools?pdn_id=11"}]