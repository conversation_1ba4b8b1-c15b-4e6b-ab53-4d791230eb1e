[{"id": 4, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "enp7s0", "metric": 100, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 5, "ipv4_subnet": "0.0.0.0", "ipv4_subnetmask": "0.0.0.0", "gateway_ipv4": "**************", "net_device": "enp7s0", "metric": 100, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 6, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.202", "metric": 401, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 7, "ipv4_subnet": "***************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.203", "metric": 402, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 8, "ipv4_subnet": "************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.30", "metric": 400, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 9, "ipv4_subnet": "***********", "ipv4_subnetmask": "***********", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": 402, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 10, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": 402, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 11, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": 402, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 13, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": 0, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 14, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": 400, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 15, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": 400, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 16, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "tun_host", "metric": 0, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 17, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_ims", "metric": 0, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 18, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_local", "metric": 0, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 19, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": 400, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}, {"id": 20, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": 400, "owner": "druid_nm", "raemis_id": 0, "dnm_managed": 0}]