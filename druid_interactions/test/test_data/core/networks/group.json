[{"id": 1, "num": "1", "name": "", "imsi_prefix": "", "description": "apn_inet", "notify_presence": 1, "disallow_mno_services": 0, "disallow_local_services": 0, "radio_zones": "", "subscription_profile_preference_id": 1, "eir_bypass": 0, "csg_ids": "", "cag_data": "", "raemis_id": 0, "subscribers": "/api/group/subscribers?group_id=1"}, {"id": 2, "num": "2", "name": "", "imsi_prefix": "", "description": "relay", "notify_presence": 1, "disallow_mno_services": 0, "disallow_local_services": 0, "radio_zones": "", "subscription_profile_preference_id": 2, "eir_bypass": 0, "csg_ids": "", "cag_data": "", "raemis_id": 0, "subscribers": "/api/group/subscribers?group_id=2"}, {"id": 4, "num": "3", "name": "", "imsi_prefix": "", "description": "eurocom", "notify_presence": 1, "disallow_mno_services": 0, "disallow_local_services": 0, "radio_zones": "", "subscription_profile_preference_id": 4, "eir_bypass": 0, "csg_ids": "", "cag_data": "", "raemis_id": 0, "subscribers": "/api/group/subscribers?group_id=4"}, {"id": 5, "num": "4", "name": "", "imsi_prefix": "", "description": "00101", "notify_presence": 1, "disallow_mno_services": 0, "disallow_local_services": 0, "radio_zones": "", "subscription_profile_preference_id": 6, "eir_bypass": 0, "csg_ids": "", "cag_data": "", "raemis_id": 0, "subscribers": "/api/group/subscribers?group_id=5"}, {"id": 6, "num": "50", "name": "", "imsi_prefix": "", "description": "00101_sd0", "notify_presence": 1, "disallow_mno_services": 0, "disallow_local_services": 0, "radio_zones": "", "subscription_profile_preference_id": 8, "eir_bypass": 0, "csg_ids": "", "cag_data": "", "raemis_id": 0, "subscribers": "/api/group/subscribers?group_id=6"}, {"id": 7, "num": "128", "name": "", "imsi_prefix": "", "description": "radisys", "notify_presence": 1, "disallow_mno_services": 0, "disallow_local_services": 0, "radio_zones": "", "subscription_profile_preference_id": 7, "eir_bypass": 0, "csg_ids": "", "cag_data": "", "raemis_id": 0, "subscribers": "/api/group/subscribers?group_id=7"}]