[{"id": 1, "mgw_id": 1, "ep_id": 1, "type": 0, "net_device": "bond0.203", "autocreate_net_device": 1, "snat_ipv4_pool_id": 0, "rcv_buf_size": ********, "snd_buf_size": ********, "external_ipv4": "", "max_segment_size": 1464, "adjust_tcp_mss": 1340, "stats_measurement_period": 5, "last_period_sent_octets": 0, "last_period_recv_octets": 0, "measurement_period": 0, "allow_traffic_shaping": 1, "max_latency": 1, "max_frame_size": 40000, "packets_per_block": 15, "nbr_of_blocks": 24, "af_blocks_per_read": 1, "packet_fanout_mode": 0, "redirection_ep": 0, "local_port": 2152, "udp_type": 0, "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": 0, "read_outgoing_packets": 0, "max_read_count": 20, "max_packets_per_read": 20, "ue_to_ue_data_enabled": 1, "subnet_routing_enabled": 0, "txqueuelen": 10000, "address_change_action": 1, "raemis_id": 1, "insert_ip_tables_rules": 0, "calc_udp_checksum": 0}, {"id": 2, "mgw_id": 1, "ep_id": 2, "type": 1, "net_device": "tun_host", "autocreate_net_device": 1, "snat_ipv4_pool_id": 0, "rcv_buf_size": ********, "snd_buf_size": ********, "external_ipv4": "", "max_segment_size": 1464, "adjust_tcp_mss": 1340, "stats_measurement_period": 5, "last_period_sent_octets": 0, "last_period_recv_octets": 0, "measurement_period": 5000, "allow_traffic_shaping": 1, "max_latency": 1, "max_frame_size": 40000, "packets_per_block": 15, "nbr_of_blocks": 24, "af_blocks_per_read": 1, "packet_fanout_mode": 0, "redirection_ep": 0, "local_port": 2152, "udp_type": 0, "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": 0, "read_outgoing_packets": 0, "max_read_count": 20, "max_packets_per_read": 20, "ue_to_ue_data_enabled": 1, "subnet_routing_enabled": 0, "txqueuelen": 10000, "address_change_action": 1, "raemis_id": 1, "insert_ip_tables_rules": 0, "calc_udp_checksum": 0}, {"id": 3, "mgw_id": 1, "ep_id": 3, "type": 1, "net_device": "tun_ims", "autocreate_net_device": 1, "snat_ipv4_pool_id": 0, "rcv_buf_size": ********, "snd_buf_size": ********, "external_ipv4": "", "max_segment_size": 1464, "adjust_tcp_mss": 1340, "stats_measurement_period": 5, "last_period_sent_octets": 0, "last_period_recv_octets": 0, "measurement_period": 5000, "allow_traffic_shaping": 1, "max_latency": 1, "max_frame_size": 40000, "packets_per_block": 15, "nbr_of_blocks": 24, "af_blocks_per_read": 1, "packet_fanout_mode": 0, "redirection_ep": 0, "local_port": 2152, "udp_type": 0, "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": 0, "read_outgoing_packets": 0, "max_read_count": 20, "max_packets_per_read": 20, "ue_to_ue_data_enabled": 1, "subnet_routing_enabled": 0, "txqueuelen": 10000, "address_change_action": 1, "raemis_id": 1, "insert_ip_tables_rules": 0, "calc_udp_checksum": 0}, {"id": 4, "mgw_id": 1, "ep_id": 100, "type": 0, "net_device": "lo", "autocreate_net_device": 1, "snat_ipv4_pool_id": 0, "rcv_buf_size": ********, "snd_buf_size": ********, "external_ipv4": "", "max_segment_size": 1464, "adjust_tcp_mss": 1340, "stats_measurement_period": 5, "last_period_sent_octets": 0, "last_period_recv_octets": 0, "measurement_period": 0, "allow_traffic_shaping": 1, "max_latency": 1, "max_frame_size": 40000, "packets_per_block": 15, "nbr_of_blocks": 24, "af_blocks_per_read": 1, "packet_fanout_mode": 0, "redirection_ep": 1, "local_port": 2154, "udp_type": 0, "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": 0, "read_outgoing_packets": 0, "max_read_count": 20, "max_packets_per_read": 20, "ue_to_ue_data_enabled": 1, "subnet_routing_enabled": 0, "txqueuelen": 10000, "address_change_action": 1, "raemis_id": 1, "insert_ip_tables_rules": 0, "calc_udp_checksum": 0}, {"id": 5, "mgw_id": 1, "ep_id": 101, "type": 1, "net_device": "tun_local", "autocreate_net_device": 1, "snat_ipv4_pool_id": 100, "rcv_buf_size": ********, "snd_buf_size": ********, "external_ipv4": "", "max_segment_size": 1464, "adjust_tcp_mss": 1340, "stats_measurement_period": 5, "last_period_sent_octets": 0, "last_period_recv_octets": 0, "measurement_period": 5000, "allow_traffic_shaping": 1, "max_latency": 1, "max_frame_size": 40000, "packets_per_block": 15, "nbr_of_blocks": 24, "af_blocks_per_read": 1, "packet_fanout_mode": 0, "redirection_ep": 0, "local_port": 2152, "udp_type": 0, "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": 0, "read_outgoing_packets": 0, "max_read_count": 20, "max_packets_per_read": 20, "ue_to_ue_data_enabled": 1, "subnet_routing_enabled": 0, "txqueuelen": 10000, "address_change_action": 1, "raemis_id": 1, "insert_ip_tables_rules": 0, "calc_udp_checksum": 0}, {"id": 6, "mgw_id": 1, "ep_id": 4, "type": 0, "net_device": "bond0.30", "autocreate_net_device": 1, "snat_ipv4_pool_id": 0, "rcv_buf_size": ********, "snd_buf_size": ********, "external_ipv4": "", "max_segment_size": 1464, "adjust_tcp_mss": 1340, "stats_measurement_period": 5, "last_period_sent_octets": 0, "last_period_recv_octets": 0, "measurement_period": 0, "allow_traffic_shaping": 1, "max_latency": 1, "max_frame_size": 40000, "packets_per_block": 15, "nbr_of_blocks": 24, "af_blocks_per_read": 1, "packet_fanout_mode": 0, "redirection_ep": 0, "local_port": 2152, "udp_type": 0, "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": 0, "read_outgoing_packets": 0, "max_read_count": 20, "max_packets_per_read": 20, "ue_to_ue_data_enabled": 1, "subnet_routing_enabled": 0, "txqueuelen": 10000, "address_change_action": 1, "raemis_id": 1, "insert_ip_tables_rules": 0, "calc_udp_checksum": 0}]