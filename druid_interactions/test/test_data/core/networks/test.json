{"id": "GB-MARL-VM-0013", "druid": {"device_id": "dauk-mrl-green-druid", "status": "OK", "aspects": {"enode_b_count": 6, "iproute_count": 16, "ipsec_secure_association_count": 0, "net_device_count": 10, "plmn_count": 1, "radio_zone_count": 0, "sgw_count": 1, "sgw_session_count": 1, "s1server_enb_count": 0, "users_count": 242, "enable_5g": false, "enable_5g_nsa": false}, "details": {"features": {"oper_state": "enabled", "product_id": "8", "system_id": "1090875381675311761", "license_id": "faf6089588675046c43a", "license_status": "0", "issue_date": "2025-01-09T12:41:26Z", "expiry_date": null, "binding_date": null, "supported_until": "2025-12-31T12:00:00Z", "max_nbr_of_subs": "270", "max_cells": "10", "max_enbs": "10", "max_pdns": "10", "max_s1_clients": "", "enbgw_max_enbs": "", "enbgw_max_active_subs": ""}, "system": {"id": 1, "oper_state": "enabled", "admin_state": "unlocked", "service_state": "Active", "system_id": "1090875381675311761", "license_id": "faf6089588675046c43a", "product_id": "*******.1.72afa9015::VIRTUAL::rhel8.10::x86_64::faf6089588675046c43a::normal::1090875381675311761", "software_version": "Raemis Enterprise *******-1, r72afa9015.", "restart_required": "0", "current_time": "2025-02-14T16:56:15.716000Z"}}, "networks": {"ip_route": [{"id": 4, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "enp7s0", "metric": "100", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 5, "ipv4_subnet": "0.0.0.0", "ipv4_subnetmask": "0.0.0.0", "gateway_ipv4": "**************", "net_device": "enp7s0", "metric": "100", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 6, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.202", "metric": "401", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 7, "ipv4_subnet": "***************", "ipv4_subnetmask": "***************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 8, "ipv4_subnet": "************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 9, "ipv4_subnet": "***********", "ipv4_subnetmask": "***********", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 10, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 11, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "402", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 13, "ipv4_subnet": "*************", "ipv4_subnetmask": "***************", "gateway_ipv4": "***************", "net_device": "bond0.203", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 14, "ipv4_subnet": "***********", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 15, "ipv4_subnet": "**********", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 16, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "0.0.0.0", "net_device": "tun_host", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 17, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_ims", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 18, "ipv4_subnet": "**********", "ipv4_subnetmask": "***********", "gateway_ipv4": "0.0.0.0", "net_device": "tun_local", "metric": "0", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 19, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}, {"id": 20, "ipv4_subnet": "*************", "ipv4_subnetmask": "*************", "gateway_ipv4": "**************", "net_device": "bond0.30", "metric": "400", "owner": "druid_nm", "raemis_id": "0", "dnm_managed": false}], "pdn": [{"id": 1, "apn": "*", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1450", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=1"}, {"id": 2, "apn": "ims", "primary_dns": "", "secondary_dns": "", "ue_mtu": "1392", "ipv4_pool_id": "2", "ep_group_id": "3", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=2"}, {"id": 3, "apn": "emergency", "primary_dns": "", "secondary_dns": "", "ue_mtu": "1392", "ipv4_pool_id": "2", "ep_group_id": "3", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=3"}, {"id": 4, "apn": "vzwims", "primary_dns": "", "secondary_dns": "", "ue_mtu": "1392", "ipv4_pool_id": "2", "ep_group_id": "3", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "**********", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=4"}, {"id": 6, "apn": "r1.denseair", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "0", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=6"}, {"id": 8, "apn": "eurocom", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=8"}, {"id": 9, "apn": "internet.00101", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=9"}, {"id": 10, "apn": "inet.mnc059.mcc001.gprs", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "1", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=10"}, {"id": 11, "apn": "radisys", "primary_dns": "*******", "secondary_dns": "*******", "ue_mtu": "1392", "ipv4_pool_id": "0", "ep_group_id": "2", "allow_multiple_connections": "0", "primary_ipv6_dns": "", "secondary_ipv6_dns": "", "ipv6_prefix_pool_id": "0", "pcscf_ipv4_addr": "", "pcscf_ipv6_addr": "", "ipv4_config_method": "", "ipv4_config_timeout": "20", "use_mac_mappings": "0", "raemis_id": "0", "sticky_ip_assignment": "0", "ipv4pools": "/api/pdn/ipv4pools?pdn_id=11"}], "group": [{"id": 1, "num": "1", "name": "", "imsi_prefix": "", "description": "apn_inet", "notify_presence": "1", "disallow_mno_services": "0", "disallow_local_services": "0", "radio_zones": "", "subscription_profile_preference_id": "1", "eir_bypass": "0", "csg_ids": "", "cag_data": "", "raemis_id": "0", "subscribers": "/api/group/subscribers?group_id=1"}, {"id": 2, "num": "2", "name": "", "imsi_prefix": "", "description": "relay", "notify_presence": "1", "disallow_mno_services": "0", "disallow_local_services": "0", "radio_zones": "", "subscription_profile_preference_id": "2", "eir_bypass": "0", "csg_ids": "", "cag_data": "", "raemis_id": "0", "subscribers": "/api/group/subscribers?group_id=2"}, {"id": 4, "num": "3", "name": "", "imsi_prefix": "", "description": "eurocom", "notify_presence": "1", "disallow_mno_services": "0", "disallow_local_services": "0", "radio_zones": "", "subscription_profile_preference_id": "4", "eir_bypass": "0", "csg_ids": "", "cag_data": "", "raemis_id": "0", "subscribers": "/api/group/subscribers?group_id=4"}, {"id": 5, "num": "4", "name": "", "imsi_prefix": "", "description": "00101", "notify_presence": "1", "disallow_mno_services": "0", "disallow_local_services": "0", "radio_zones": "", "subscription_profile_preference_id": "6", "eir_bypass": "0", "csg_ids": "", "cag_data": "", "raemis_id": "0", "subscribers": "/api/group/subscribers?group_id=5"}, {"id": 6, "num": "50", "name": "", "imsi_prefix": "", "description": "00101_sd0", "notify_presence": "1", "disallow_mno_services": "0", "disallow_local_services": "0", "radio_zones": "", "subscription_profile_preference_id": "8", "eir_bypass": "0", "csg_ids": "", "cag_data": "", "raemis_id": "0", "subscribers": "/api/group/subscribers?group_id=6"}, {"id": 7, "num": "128", "name": "", "imsi_prefix": "", "description": "radisys", "notify_presence": "1", "disallow_mno_services": "0", "disallow_local_services": "0", "radio_zones": "", "subscription_profile_preference_id": "7", "eir_bypass": "0", "csg_ids": "", "cag_data": "", "raemis_id": "0", "subscribers": "/api/group/subscribers?group_id=7"}], "ipv4_pool": [{"id": 1, "name": "", "first_ip": "*************", "last_ip": "***************", "used_ips": "1", "raemis_id": "0"}, {"id": 2, "name": "IMS IPv4 pool", "first_ip": "**********", "last_ip": "**************", "used_ips": "0", "raemis_id": "0"}, {"id": 100, "name": "Local Apps IPv4 pool", "first_ip": "**********", "last_ip": "**************", "used_ips": "1", "raemis_id": "0"}], "mgw_endpoint": [{"id": 1, "mgw_id": "1", "ep_id": "1", "type": "0", "net_device": "bond0.203", "autocreate_net_device": "1", "snat_ipv4_pool_id": "0", "rcv_buf_size": "********", "snd_buf_size": "********", "external_ipv4": "", "max_segment_size": "1464", "adjust_tcp_mss": "1340", "stats_measurement_period": "5", "last_period_sent_octets": "0", "last_period_recv_octets": "0", "measurement_period": "0", "allow_traffic_shaping": "1", "max_latency": "1", "max_frame_size": "40000", "packets_per_block": "15", "nbr_of_blocks": "24", "af_blocks_per_read": "1", "packet_fanout_mode": "0", "redirection_ep": "0", "local_port": "2152", "udp_type": "0", "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": "0", "read_outgoing_packets": "0", "max_read_count": "20", "max_packets_per_read": "20", "ue_to_ue_data_enabled": "1", "subnet_routing_enabled": false, "txqueuelen": "10000", "address_change_action": "1", "raemis_id": "1", "insert_ip_tables_rules": "0", "calc_udp_checksum": "0"}, {"id": 2, "mgw_id": "1", "ep_id": "2", "type": "1", "net_device": "tun_host", "autocreate_net_device": "1", "snat_ipv4_pool_id": "0", "rcv_buf_size": "********", "snd_buf_size": "********", "external_ipv4": "", "max_segment_size": "1464", "adjust_tcp_mss": "1340", "stats_measurement_period": "5", "last_period_sent_octets": "0", "last_period_recv_octets": "0", "measurement_period": "5000", "allow_traffic_shaping": "1", "max_latency": "1", "max_frame_size": "40000", "packets_per_block": "15", "nbr_of_blocks": "24", "af_blocks_per_read": "1", "packet_fanout_mode": "0", "redirection_ep": "0", "local_port": "2152", "udp_type": "0", "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": "0", "read_outgoing_packets": "0", "max_read_count": "20", "max_packets_per_read": "20", "ue_to_ue_data_enabled": "1", "subnet_routing_enabled": false, "txqueuelen": "10000", "address_change_action": "1", "raemis_id": "1", "insert_ip_tables_rules": "0", "calc_udp_checksum": "0"}, {"id": 3, "mgw_id": "1", "ep_id": "3", "type": "1", "net_device": "tun_ims", "autocreate_net_device": "1", "snat_ipv4_pool_id": "0", "rcv_buf_size": "********", "snd_buf_size": "********", "external_ipv4": "", "max_segment_size": "1464", "adjust_tcp_mss": "1340", "stats_measurement_period": "5", "last_period_sent_octets": "0", "last_period_recv_octets": "0", "measurement_period": "5000", "allow_traffic_shaping": "1", "max_latency": "1", "max_frame_size": "40000", "packets_per_block": "15", "nbr_of_blocks": "24", "af_blocks_per_read": "1", "packet_fanout_mode": "0", "redirection_ep": "0", "local_port": "2152", "udp_type": "0", "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": "0", "read_outgoing_packets": "0", "max_read_count": "20", "max_packets_per_read": "20", "ue_to_ue_data_enabled": "1", "subnet_routing_enabled": false, "txqueuelen": "10000", "address_change_action": "1", "raemis_id": "1", "insert_ip_tables_rules": "0", "calc_udp_checksum": "0"}, {"id": 4, "mgw_id": "1", "ep_id": "100", "type": "0", "net_device": "lo", "autocreate_net_device": "1", "snat_ipv4_pool_id": "0", "rcv_buf_size": "********", "snd_buf_size": "********", "external_ipv4": "", "max_segment_size": "1464", "adjust_tcp_mss": "1340", "stats_measurement_period": "5", "last_period_sent_octets": "0", "last_period_recv_octets": "0", "measurement_period": "0", "allow_traffic_shaping": "1", "max_latency": "1", "max_frame_size": "40000", "packets_per_block": "15", "nbr_of_blocks": "24", "af_blocks_per_read": "1", "packet_fanout_mode": "0", "redirection_ep": "1", "local_port": "2154", "udp_type": "0", "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": "0", "read_outgoing_packets": "0", "max_read_count": "20", "max_packets_per_read": "20", "ue_to_ue_data_enabled": "1", "subnet_routing_enabled": false, "txqueuelen": "10000", "address_change_action": "1", "raemis_id": "1", "insert_ip_tables_rules": "0", "calc_udp_checksum": "0"}, {"id": 5, "mgw_id": "1", "ep_id": "101", "type": "1", "net_device": "tun_local", "autocreate_net_device": "1", "snat_ipv4_pool_id": "100", "rcv_buf_size": "********", "snd_buf_size": "********", "external_ipv4": "", "max_segment_size": "1464", "adjust_tcp_mss": "1340", "stats_measurement_period": "5", "last_period_sent_octets": "0", "last_period_recv_octets": "0", "measurement_period": "5000", "allow_traffic_shaping": "1", "max_latency": "1", "max_frame_size": "40000", "packets_per_block": "15", "nbr_of_blocks": "24", "af_blocks_per_read": "1", "packet_fanout_mode": "0", "redirection_ep": "0", "local_port": "2152", "udp_type": "0", "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": "0", "read_outgoing_packets": "0", "max_read_count": "20", "max_packets_per_read": "20", "ue_to_ue_data_enabled": "1", "subnet_routing_enabled": false, "txqueuelen": "10000", "address_change_action": "1", "raemis_id": "1", "insert_ip_tables_rules": "0", "calc_udp_checksum": "0"}, {"id": 6, "mgw_id": "1", "ep_id": "4", "type": "0", "net_device": "bond0.30", "autocreate_net_device": "1", "snat_ipv4_pool_id": "0", "rcv_buf_size": "********", "snd_buf_size": "********", "external_ipv4": "", "max_segment_size": "1464", "adjust_tcp_mss": "1340", "stats_measurement_period": "5", "last_period_sent_octets": "0", "last_period_recv_octets": "0", "measurement_period": "0", "allow_traffic_shaping": "1", "max_latency": "1", "max_frame_size": "40000", "packets_per_block": "15", "nbr_of_blocks": "24", "af_blocks_per_read": "1", "packet_fanout_mode": "0", "redirection_ep": "0", "local_port": "2152", "udp_type": "0", "local_ipv4": "", "additional_local_ipv4s": "", "omit_src_addr_in_arp": "0", "read_outgoing_packets": "0", "max_read_count": "20", "max_packets_per_read": "20", "ue_to_ue_data_enabled": "1", "subnet_routing_enabled": false, "txqueuelen": "10000", "address_change_action": "1", "raemis_id": "1", "insert_ip_tables_rules": "0", "calc_udp_checksum": "0"}], "net_device": [{"id": 1, "admin_state": "unlocked", "oper_state": "enabled", "mac": "", "device": "tun_host", "parent_device": "", "vlan_id": "0", "bootproto": "static", "ip": "*************", "netmask": "*************", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "1", "ipv6_forwarding_enabled": "0", "owner": "mgw", "device_type": "tun", "raemis_id": "1", "cidr": 23}, {"id": 2, "admin_state": "unlocked", "oper_state": "enabled", "mac": "", "device": "tun_ims", "parent_device": "", "vlan_id": "0", "bootproto": "static", "ip": "**********", "netmask": "***********", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "1", "ipv6_forwarding_enabled": "0", "owner": "mgw", "device_type": "tun", "raemis_id": "1", "cidr": 16}, {"id": 3, "admin_state": "unlocked", "oper_state": "enabled", "mac": "", "device": "tun_local", "parent_device": "", "vlan_id": "0", "bootproto": "static", "ip": "**********", "netmask": "***********", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "1", "ipv6_forwarding_enabled": "0", "owner": "mgw", "device_type": "tun", "raemis_id": "1", "cidr": 16}, {"id": 59993, "admin_state": "unlocked", "oper_state": "enabled", "mac": "9A:FC:11:9D:1F:60", "device": "bond0", "parent_device": "", "vlan_id": "0", "bootproto": "none", "ip": "", "netmask": "", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "0", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "bond", "raemis_id": "1", "cidr": null}, {"id": 59994, "admin_state": "unlocked", "oper_state": "enabled", "mac": "9A:FC:11:9D:1F:60", "device": "bond0.30", "parent_device": "bond0", "vlan_id": "30", "bootproto": "static", "ip": "**************", "netmask": "*************", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "0", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "vlan", "raemis_id": "1", "cidr": 24}, {"id": 59995, "admin_state": "unlocked", "oper_state": "enabled", "mac": "9A:FC:11:9D:1F:60", "device": "bond0.203", "parent_device": "bond0", "vlan_id": "203", "bootproto": "static", "ip": "***************", "netmask": "***************", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "0", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "vlan", "raemis_id": "1", "cidr": 29}, {"id": 59996, "admin_state": "unlocked", "oper_state": "enabled", "mac": "9A:FC:11:9D:1F:60", "device": "bond0.202", "parent_device": "bond0", "vlan_id": "202", "bootproto": "static", "ip": "*************", "netmask": "***************", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "1", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "vlan", "raemis_id": "1", "cidr": 28}, {"id": 59997, "admin_state": "unlocked", "oper_state": "enabled", "mac": "36:BB:B2:09:56:A0", "device": "enp9s0", "parent_device": "", "vlan_id": "0", "bootproto": "none", "ip": "", "netmask": "", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "0", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "ethernet", "raemis_id": "1", "cidr": null}, {"id": 59998, "admin_state": "unlocked", "oper_state": "enabled", "mac": "9A:FC:11:9D:1F:60", "device": "enp8s0", "parent_device": "", "vlan_id": "0", "bootproto": "none", "ip": "", "netmask": "", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "0", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "ethernet", "raemis_id": "1", "cidr": null}, {"id": 59999, "admin_state": "unlocked", "oper_state": "enabled", "mac": "52:54:00:09:13:60", "device": "enp7s0", "parent_device": "", "vlan_id": "0", "bootproto": "static", "ip": "*************", "netmask": "***************", "ipv6": "", "nat_enabled": false, "ipv4_forwarding_enabled": "0", "ipv6_forwarding_enabled": "0", "owner": "druid_nm", "device_type": "ethernet", "raemis_id": "1", "cidr": 28}], "network_slice": [{"id": 1, "name": "eMBB", "plmn_id": "00159", "sst": "1", "sd": "16777215", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 2, "name": "URLLC", "plmn_id": "", "sst": "2", "sd": "16777215", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 3, "name": "MIoT", "plmn_id": "", "sst": "3", "sd": "16777215", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 4, "name": "V2X", "plmn_id": "", "sst": "4", "sd": "16777215", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 5, "name": "plmn_00101", "plmn_id": "00101", "sst": "1", "sd": "2", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 6, "name": "eurecom_sd", "plmn_id": "00159", "sst": "1", "sd": "3", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 7, "name": "plmn_00101_0", "plmn_id": "00101", "sst": "1", "sd": "0", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}, {"id": 8, "name": "radisys", "plmn_id": "00159", "sst": "1", "sd": "1", "raemis_id": "0", "overload_action": "0", "traffic_load_reduction_indication": "0"}], "subscription_profile": [{"id": 1, "pdn_type": "0", "apn": "*", "name": "", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "1000000", "ul_apn_ambr": "1000000", "pgw_address": "", "apply_to_all_subs": 1, "plmn_id": "00159", "scef_id": "", "network_slice_id": 1, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "2", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 2, "pdn_type": "0", "apn": "ims", "name": "", "qci": "5", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 1, "plmn_id": "00159", "scef_id": "", "network_slice_id": 1, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 3, "pdn_type": "0", "apn": "vzwims", "name": "", "qci": "5", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 1, "plmn_id": "", "scef_id": "", "network_slice_id": 1, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "2", "up_confidentiality_protection": "2", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 4, "pdn_type": "0", "apn": "inet", "name": "", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "", "scef_id": "", "network_slice_id": 1, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 9, "pdn_type": "0", "apn": "ims", "name": "ims_00101", "qci": "5", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 1, "plmn_id": "00101", "scef_id": "", "network_slice_id": 5, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 10, "pdn_type": "0", "apn": "r1.denseair", "name": "", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "", "scef_id": "", "network_slice_id": 0, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 12, "pdn_type": "0", "apn": "eurocom", "name": "", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "", "scef_id": "", "network_slice_id": 6, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 13, "pdn_type": "0", "apn": "internet.00101", "name": "00101", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "1000000", "ul_apn_ambr": "1000000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "00101", "scef_id": "", "network_slice_id": 7, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 14, "pdn_type": "0", "apn": "inet.mnc059.mcc001.gprs", "name": "", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "100000", "ul_apn_ambr": "100000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "", "scef_id": "", "network_slice_id": 0, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "1", "up_confidentiality_protection": "1", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 15, "pdn_type": "0", "apn": "internet.00101", "name": "sd0_internet.00101", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "1000000", "ul_apn_ambr": "1000000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "00101", "scef_id": "", "network_slice_id": 7, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "2", "up_confidentiality_protection": "2", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}, {"id": 16, "pdn_type": "0", "apn": "radisys", "name": "", "qci": "9", "priority": "15", "may_trigger_preemption": "0", "preemptable": "0", "dl_apn_ambr": "1000000", "ul_apn_ambr": "1090000", "pgw_address": "", "apply_to_all_subs": 0, "plmn_id": "", "scef_id": "", "network_slice_id": 8, "monitoring_key": "", "total_granted_octets_monitoring_interval": "10000000000", "input_granted_octets_monitoring_interval": "0", "output_granted_octets_monitoring_interval": "0", "granted_time_monitoring_interval": "0", "up_integrity_protection": "2", "up_confidentiality_protection": "2", "online": "0", "rating_group": "0", "service_identifier": "0", "raemis_id": "0"}]}, "segw": {"ipsec_child_config": [], "ipsec_child_config_proposal": [], "ipsec_certificate": [], "ipsec_ikeconfig_proposal": [], "ipsec_peer_config": [], "ipsec_private_key": [], "ipsec_secure_association": [], "ipsec_proposal": [{"id": 1, "proposal": "aes128-sha1-modp2048"}, {"id": 2, "proposal": "aes128-sha256-modp2048"}, {"id": 3, "proposal": "aes256-sha1-modp2048"}, {"id": 4, "proposal": "aes256-sha256-modp2048"}], "s1client": []}, "sessions": [{"id": 111, "creation_date": "", "bearer_id": 5, "uplink_flow_id": 881, "downlink_flow_id": 882, "mbr_dl": 1000000, "mbr_ul": 1000000, "qci": 9, "sgw_id": 1, "imsi": "001590140010806", "s11_teid": 1186000572, "s5s8_teid": 1186000573, "apn": "*.mnc059.mcc001.gprs", "paa_ipv4": "*************12", "ecgi": "00159:269312", "tai": "00159:10", "mgw_flow_group_id": 111, "ambr_dl": 1000000, "ambr_ul": 1000000, "s11_cp_ipv4": "127.0.0.1", "s5s8_cp_teid": 1186000574, "sgw": {"id": 1, "name": "SGW", "admin_state": "unlocked", "oper_state": "enabled", "node_id": 0, "creation_date": "", "mgw_ctrl_net_device": "lo", "mgw_ctrl_local_port": "8011", "mgw_ctrl_active_url": "1", "mgw_ctrl_url1": "127.0.0.1:8013", "mgw_ctrl_url2": "", "mgw_ctrl_url3": "", "dscp": "0", "sctp_read_count": "200", "s11_gtpv2_id": "1", "s5s8_gtpv2_id": "1", "s1u_ep_group_id": "1", "s5s8_ep_group_id": "1", "cdr_periodicity": "0", "cdr_failure_handling": "0", "cdr_peak_data_sampling_period": "0", "s1u_dp_ipv4_enabled": "1", "s1u_dp_ipv6_enabled": "1", "mgw_ctrl_flow_attr_1": "1", "mgw_ctrl_flow_attr_2": "2", "mgw_ctrl_flow_attr_3": "3", "mgw_ctrl_flow_attr_4": "6", "raemis_id": 0}}], "enodebs": [{"id": 2, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269570, "enb_id": 2, "enodeb": {"id": 2, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1053, "name": "eNB-1053", "sctp_address": "*************:54819", "last_inform_time": null}}, {"id": 7, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 0, "cell_id": 268800, "enb_id": 3, "enodeb": {"id": 3, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1050, "name": "at&t-marlow-1050", "sctp_address": "**********:36412", "last_inform_time": null}}, {"id": 8, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 1, "cell_id": 1050, "enb_id": 4, "enodeb": {"id": 4, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 4, "name": "at&t-marlow-1", "sctp_address": "**********:36412", "last_inform_time": null}}, {"id": 10, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 67502337, "enb_id": 6, "enodeb": {"id": 6, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 263681, "name": "AS1030 Cetin NQT", "sctp_address": "***************:36412", "last_inform_time": null}}, {"id": 14, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269057, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 15, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269058, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 17, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269056, "enb_id": 7, "enodeb": {"id": 7, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1051, "name": "green_veNB", "sctp_address": "***********:36412", "last_inform_time": null}}, {"id": 18, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269569, "enb_id": 2, "enodeb": {"id": 2, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1053, "name": "eNB-1053", "sctp_address": "*************:54819", "last_inform_time": null}}, {"id": 19, "oper_state": "enabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269312, "enb_id": 8, "enodeb": {"id": 8, "oper_state": "enabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1052, "name": "eNB-1052", "sctp_address": "*************:48449", "last_inform_time": null}}, {"id": 20, "oper_state": "disabled", "admin_state": "unlocked", "name": "", "tac": 10, "cell_id": 269568, "enb_id": 2, "enodeb": {"id": 2, "oper_state": "disabled", "admin_state": "unlocked", "plmn_id": "00159", "identity": 1053, "name": "eNB-1053", "sctp_address": "*************:54819", "last_inform_time": null}}], "created_at": "2025-02-14T16:56:52.063112Z"}, "vsr": null, "acp": null}