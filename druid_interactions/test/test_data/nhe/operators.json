[{"id": 2, "oper_state": 2, "admin_state": 2, "name": "315010-eNB-1052-CN-DruidIPv6", "s1mme_net_device": "segw_tunnel1_c", "local_port": 0, "ep_group_id": 2, "mme1_ip": "fd00:1000::3", "mme1_port": 36412, "connect_retry_time": 10, "s1_setup_timeout": 2000, "s1_client_type": 0, "enb_name": "eNB-1052", "plmn_id": "315010", "tai_list": "315010:10", "cell_identity": 269312, "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0, "remote_gummeis": "315010:0:0", "relative_capacity_1": 255, "default_route": 0, "external_ipv4": "", "external_ipv6": "", "report_periodicity": 60, "report_last_record_id": 0, "support_emergency_calls": 1, "topology_hiding": 0, "support_pws": 1, "emergency_area_list": "", "ue_context_release_timeout": 3000, "sctp_read_count": 200, "tos": 0, "pws_overwrite_plmn": 0, "raemis_id": 0}, {"id": 4, "oper_state": 2, "admin_state": 2, "name": "315010-eNB-1053-CN-DruidIPv4", "s1mme_net_device": "segw_ipsec_ipv", "local_port": 0, "ep_group_id": 6, "mme1_ip": "**************", "mme1_port": 36412, "connect_retry_time": 10, "s1_setup_timeout": 2000, "s1_client_type": 0, "enb_name": "eNB-1053", "plmn_id": "315010", "tai_list": "315010:10", "cell_identity": 269568, "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0, "remote_gummeis": "315010:0:0", "relative_capacity_1": 255, "default_route": 0, "external_ipv4": "", "external_ipv6": "", "report_periodicity": 60, "report_last_record_id": 0, "support_emergency_calls": 1, "topology_hiding": 0, "support_pws": 1, "emergency_area_list": "", "ue_context_release_timeout": 3000, "sctp_read_count": 200, "tos": 0, "pws_overwrite_plmn": 0, "raemis_id": 0}, {"id": 1, "oper_state": 2, "admin_state": 2, "name": "eNB-1052", "s1mme_net_device": "bond0.55", "local_port": 0, "ep_group_id": 4, "mme1_ip": "***************", "mme1_port": 36412, "connect_retry_time": 10, "s1_setup_timeout": 2000, "s1_client_type": 0, "enb_name": "eNB-1052", "plmn_id": "00159", "tai_list": "00159:10", "cell_identity": 269312, "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0, "remote_gummeis": "00159:0:0", "relative_capacity_1": 255, "default_route": 0, "external_ipv4": "", "external_ipv6": "", "report_periodicity": 60, "report_last_record_id": 0, "support_emergency_calls": 1, "topology_hiding": 1, "support_pws": 1, "emergency_area_list": "", "ue_context_release_timeout": 3000, "sctp_read_count": 200, "tos": 0, "pws_overwrite_plmn": 0, "raemis_id": 0}, {"id": 3, "oper_state": 2, "admin_state": 2, "name": "eNB-1053", "s1mme_net_device": "bond0.55", "local_port": 0, "ep_group_id": 4, "mme1_ip": "***************", "mme1_port": 36412, "connect_retry_time": 10, "s1_setup_timeout": 2000, "s1_client_type": 0, "enb_name": "eNB-1053", "plmn_id": "00159", "tai_list": "00159:10", "cell_identity": 269568, "max_downlink_bandwidth": 0, "max_uplink_bandwidth": 0, "remote_gummeis": "00159:0:0", "relative_capacity_1": 255, "default_route": 0, "external_ipv4": "", "external_ipv6": "", "report_periodicity": 60, "report_last_record_id": 0, "support_emergency_calls": 1, "topology_hiding": 0, "support_pws": 1, "emergency_area_list": "", "ue_context_release_timeout": 3000, "sctp_read_count": 200, "tos": 0, "pws_overwrite_plmn": 0, "raemis_id": 0}]