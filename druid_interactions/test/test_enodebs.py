import unittest

from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.queries.enodebs import ENodeBQuery
from druid_interactions.queries.enodebs import ENodeBReport
from druid_interactions.queries.enodebs import ENodeBTrxQuery
from druid_interactions.test.test_networks import NetDeviceQueryTests


class ENodeBReportTests(unittest.IsolatedAsyncioTestCase):

    enodebs = [
        {
            "id": 1,
            "oper_state": 2,
            "admin_state": 2,
            "plmn_id": "00159",
            "identity": 1052,
            "enb_type": 0,
            "name": "eNB-1052",
            "ManufacturerOUI": "",
            "SerialNumber": "",
            "ProductClass": "",
            "HardwareVersion": "",
            "SoftwareVersion": "",
            "S1SigLinkServerList": "",
            "Temperature": 0,
            "Licenses_EnabledFeatures": "",
            "SupportedSystems": "",
            "sctp_address": "*************:35322",
            "last_inform_time": "",
            "OperatorName": "",
            "RUWhiteList": "",
            "LocalTimeZone": "",
            "reported_csg_ids": "",
            "Description": "",
            "Tenant": "",
            "Venue": "",
            "location_latitude": "",
            "location_longitude": "",
            "location_height": "",
            "TimeReference": "",
            "BootstrapFirmware_id": 0,
            "FCCID": "",
            "secondary_plmn_id": "",
            "LastResetReason": "",
            "ModelName": "",
            "TunnelIpAddress": "",
            "Leds_Status": "",
            "Running_Firmware_Version": "",
            "Backup_Firmware_Version": "",
            "Factory_Firmware_Version": "",
            "reported_tai_list": "00159:10",
        },
        {
            "id": 2,
            "oper_state": 2,
            "admin_state": 2,
            "plmn_id": "00159",
            "identity": 1053,
            "enb_type": 0,
            "name": "eNB-1053",
            "ManufacturerOUI": "",
            "SerialNumber": "",
            "ProductClass": "",
            "HardwareVersion": "",
            "SoftwareVersion": "",
            "S1SigLinkServerList": "",
            "Temperature": 0,
            "Licenses_EnabledFeatures": "",
            "SupportedSystems": "",
            "sctp_address": "*************:37386",
            "last_inform_time": "",
            "OperatorName": "",
            "RUWhiteList": "",
            "LocalTimeZone": "",
            "reported_csg_ids": "",
            "Description": "",
            "Tenant": "",
            "Venue": "",
            "location_latitude": "",
            "location_longitude": "",
            "location_height": "",
            "TimeReference": "",
            "BootstrapFirmware_id": 0,
            "FCCID": "",
            "secondary_plmn_id": "",
            "LastResetReason": "",
            "ModelName": "",
            "TunnelIpAddress": "",
            "Leds_Status": "",
            "Running_Firmware_Version": "",
            "Backup_Firmware_Version": "",
            "Factory_Firmware_Version": "",
            "reported_tai_list": "00159:10",
        },
        {
            "id": 3,
            "oper_state": 2,
            "admin_state": 2,
            "plmn_id": "00159",
            "identity": 1050,
            "enb_type": 0,
            "name": "at&t-marlow-1050",
            "ManufacturerOUI": "",
            "SerialNumber": "",
            "ProductClass": "",
            "HardwareVersion": "",
            "SoftwareVersion": "",
            "S1SigLinkServerList": "",
            "Temperature": 0,
            "Licenses_EnabledFeatures": "",
            "SupportedSystems": "",
            "sctp_address": "**********:36412",
            "last_inform_time": "",
            "OperatorName": "",
            "RUWhiteList": "",
            "LocalTimeZone": "",
            "reported_csg_ids": "",
            "Description": "",
            "Tenant": "",
            "Venue": "",
            "location_latitude": "",
            "location_longitude": "",
            "location_height": "",
            "TimeReference": "",
            "BootstrapFirmware_id": 0,
            "FCCID": "",
            "secondary_plmn_id": "",
            "LastResetReason": "",
            "ModelName": "",
            "TunnelIpAddress": "",
            "Leds_Status": "",
            "Running_Firmware_Version": "",
            "Backup_Firmware_Version": "",
            "Factory_Firmware_Version": "",
            "reported_tai_list": "00159:1,00159:10",
        },
        {
            "id": 4,
            "oper_state": 2,
            "admin_state": 2,
            "plmn_id": "00159",
            "identity": 4,
            "enb_type": 0,
            "name": "at&t-marlow-1",
            "ManufacturerOUI": "",
            "SerialNumber": "",
            "ProductClass": "",
            "HardwareVersion": "",
            "SoftwareVersion": "",
            "S1SigLinkServerList": "",
            "Temperature": 0,
            "Licenses_EnabledFeatures": "",
            "SupportedSystems": "",
            "sctp_address": "**********:36412",
            "last_inform_time": "",
            "OperatorName": "",
            "RUWhiteList": "",
            "LocalTimeZone": "",
            "reported_csg_ids": "",
            "Description": "",
            "Tenant": "",
            "Venue": "",
            "location_latitude": "",
            "location_longitude": "",
            "location_height": "",
            "TimeReference": "",
            "BootstrapFirmware_id": 0,
            "FCCID": "",
            "secondary_plmn_id": "",
            "LastResetReason": "",
            "ModelName": "",
            "TunnelIpAddress": "",
            "Leds_Status": "",
            "Running_Firmware_Version": "",
            "Backup_Firmware_Version": "",
            "Factory_Firmware_Version": "",
            "reported_tai_list": "00159:1,00159:10",
        },
        {
            "id": 6,
            "oper_state": 2,
            "admin_state": 2,
            "plmn_id": "00159",
            "identity": 263681,
            "enb_type": 0,
            "name": "AS1030 Cetin NQT",
            "ManufacturerOUI": "",
            "SerialNumber": "",
            "ProductClass": "",
            "HardwareVersion": "",
            "SoftwareVersion": "",
            "S1SigLinkServerList": "",
            "Temperature": 0,
            "Licenses_EnabledFeatures": "",
            "SupportedSystems": "",
            "sctp_address": "***************:36412",
            "last_inform_time": "",
            "OperatorName": "",
            "RUWhiteList": "",
            "LocalTimeZone": "",
            "reported_csg_ids": "",
            "Description": "",
            "Tenant": "",
            "Venue": "",
            "location_latitude": "",
            "location_longitude": "",
            "location_height": "",
            "TimeReference": "",
            "BootstrapFirmware_id": 0,
            "FCCID": "",
            "secondary_plmn_id": "",
            "LastResetReason": "",
            "ModelName": "",
            "TunnelIpAddress": "",
            "Leds_Status": "",
            "Running_Firmware_Version": "",
            "Backup_Firmware_Version": "",
            "Factory_Firmware_Version": "",
            "reported_tai_list": "00159:10",
        },
        {
            "id": 7,
            "oper_state": 2,
            "admin_state": 2,
            "plmn_id": "00159",
            "identity": 1051,
            "enb_type": 0,
            "name": "green_veNB",
            "ManufacturerOUI": "",
            "SerialNumber": "",
            "ProductClass": "",
            "HardwareVersion": "",
            "SoftwareVersion": "",
            "S1SigLinkServerList": "",
            "Temperature": 0,
            "Licenses_EnabledFeatures": "",
            "SupportedSystems": "",
            "sctp_address": "***********:36412",
            "last_inform_time": "",
            "OperatorName": "",
            "RUWhiteList": "",
            "LocalTimeZone": "",
            "reported_csg_ids": "",
            "Description": "",
            "Tenant": "",
            "Venue": "",
            "location_latitude": "",
            "location_longitude": "",
            "location_height": "",
            "TimeReference": "",
            "BootstrapFirmware_id": 0,
            "FCCID": "",
            "secondary_plmn_id": "",
            "LastResetReason": "",
            "ModelName": "",
            "TunnelIpAddress": "",
            "Leds_Status": "",
            "Running_Firmware_Version": "",
            "Backup_Firmware_Version": "",
            "Factory_Firmware_Version": "",
            "reported_tai_list": "00159:10,00159:12",
        },
    ]

    enodeb_trxs = [
        {
            "id": 1,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269334,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 2,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269570,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 2,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 3,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269336,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 4,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269502,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 5,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269501,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 6,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269333,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 7,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 0,
            "cell_id": 268800,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 3,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 8,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 1,
            "cell_id": 1050,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 4,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 10,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 67502337,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 6,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 11,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269335,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 13,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269338,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 14,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269057,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 7,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 15,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269058,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 7,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 16,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269312,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 1,
            "SectorID": 0,
            "csg_id": 0,
        },
        {
            "id": 17,
            "oper_state": 2,
            "admin_state": 2,
            "name": "",
            "tac": 10,
            "cell_id": 269056,
            "PhyCellID": "",
            "PhyCellIDInUse": 0,
            "location": "",
            "mac": "",
            "BandsSupported": "",
            "downlink_bandwidth": 0,
            "uplink_bandwidth": 0,
            "downlink_earfcn": 0,
            "uplink_earfcn": 0,
            "mimo_group": 0,
            "MaxTxPower": 0,
            "MinTxPower": 0,
            "enb_id": 7,
            "SectorID": 0,
            "csg_id": 0,
        },
    ]

    def test_enodeb_query(self):
        query = ENodeBQuery()
        rv = query.normalize_data(self.enodebs[0], NetDeviceQueryTests.schema)
        self.assertEqual(rv.get("admin_state"), "unlocked")
        self.assertEqual(rv.get("oper_state"), "enabled")
        self.assertGreaterEqual(
            set(rv),
            {
                "id",
                "name",
                "admin_state",
                "oper_state",
                "last_inform_time",
                "plmn_id",
                "identity",
            },
        )

    def test_enodeb_trx_query(self):
        query = ENodeBTrxQuery()
        rv = query.normalize_data(self.enodeb_trxs[0], NetDeviceQueryTests.schema)
        self.assertEqual(rv.get("admin_state"), "unlocked")
        self.assertEqual(rv.get("oper_state"), "enabled")
        self.assertGreaterEqual(
            set(rv), {"id", "name", "admin_state", "oper_state", "cell_id", "enb_id", "tac"}
        )

    def test_enodeb_items(self):
        report = dict(
            ENodeBQuery=self.enodebs,
            ENodeBTrxQuery=self.enodeb_trxs,
        )
        survey = Survey(
            device_id="dauk-mrl-green-druid-core", report_type="ENodeBReport", report=report
        )
        rv = ENodeBReport.items(survey.report)

        self.assertIsInstance(rv, dict)
        self.assertTrue(all(isinstance(i, int) for i in rv), rv)
        self.assertTrue(all(isinstance(i.get("enodeb"), dict) for i in rv.values()), rv)

    async def test_enodeb_report(self):
        instance = Instance(name="dauk-mrl-green-druid", host="*************")

        report = ENodeBReport()
        report.queries[ENodeBQuery].get_object = unittest.mock.AsyncMock()
        report.queries[ENodeBQuery].get_object.return_value = unittest.mock.Mock()

        report.queries[ENodeBQuery].get_object.return_value.json.return_value = self.enodebs
        report.queries[ENodeBTrxQuery].get_object = unittest.mock.AsyncMock()
        report.queries[ENodeBTrxQuery].get_object.return_value = unittest.mock.Mock()
        report.queries[
            ENodeBTrxQuery
        ].get_object.return_value.json.return_value = self.enodeb_trxs

        data = await report.get_report_data(instance)
        result = report.build_report(data)

        report.queries[ENodeBQuery].get_object.assert_called_once()
        report.queries[ENodeBTrxQuery].get_object.assert_called_once()

        enodeb_ids = {id for i in result["ENodeBQuery"] if (id := i.get("id")) is not None}
        self.assertEqual(len(enodeb_ids), len(result["ENodeBQuery"]))
        for n, enodeb_trx in enumerate(result["ENodeBTrxQuery"]):
            with self.subTest(n=n):
                self.assertIn("enb_id", enodeb_trx)
                self.assertIn(enodeb_trx["enb_id"], enodeb_ids)
