import asyncio
import datetime
import json
import logging
import sys
import uuid
from collections import ChainMap
from collections.abc import Generator
from typing import Any
from typing import Optional

from da_common.config import Config
from da_common.interfaces import StatusEvent
from da_common.models import Status
from dal_pubsub.pubsub import PubSub
from druid_interactions.creds import get_credentials
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.models.survey import Survey
from druid_interactions.pool import PoolFactory
from druid_interactions.queries.alarms import AlarmsQuery
from druid_interactions.queries.alarms import StatusReport
from druid_interactions.queries.enodebs import ENodeBReport
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.reporter import Reporter
from druid_interactions.types import AlarmType
from druid_interactions.types import LicenceExpiryType
from druid_interactions.types import Severity
from metrics_collector.api_schema.models import AlarmStatus


class Watchdog(Reporter):
    """
    Watchdog monitors Druid instances, collects alarms, and publishes status
    events.
    """

    def __init__(self, config: Config, pool: Optional[Any] = None, interval: int = 30):
        """
        Initialize the Watchdog instance.

        Args:
            config: Configuration object containing settings for Druid
                interactions.
            pool: Optional database connection pool to use for database
                operations.
            interval: Time interval (in seconds) between watchdog runs.
        """
        super().__init__(config=config, pool=pool, interval=interval)
        self.logger = logging.getLogger("druid_interactions.watchdog")
        self.alarms: dict[str, dict[str, AlarmEntry]] = {}
        self.pubsub: Optional[PubSub] = None

    @staticmethod
    def get_alarm_entry(record: dict = {}, **kwargs) -> AlarmEntry:
        """
        Create an AlarmEntry from given data.

        Args:
            record: Dictionary containing alarm data (optional).
            kwargs: Additional parameters for the alarm entry.

        Returns:
            An AlarmEntry instance representing the alarm.
        """
        params = {**record, **kwargs}
        return AlarmEntry(**params)

    @staticmethod
    def device_status(
        alarms: list[AlarmEntry],
        admin_state: str = "",
        service_state: str = "",
        oper_state: str = "",
        restart_required: Optional[bool] = None,
    ) -> Status:
        """
        Determine the device status based on alarms and operational states.

        Args:
            alarms: list of current AlarmEntry instances for the device.
            admin_state: Current administrative state of the device.
            service_state: Current service state of the device.
            oper_state: Current operational state of the device.
            restart_required: Flag indicating if a restart is required (optional).

        Returns:
            A Status instance representing the current status of the device.
        """
        if any(alarm.severity == Severity.CRITICAL for alarm in alarms):
            return Status.CRITICAL
        if any(alarm.severity == Severity.MAJOR for alarm in alarms):
            return Status.ERROR
        if alarms:
            return Status.WARNING

        if any(
            state in x.lower()
            for state, x in zip(
                ["shutdown", "unknown", "disabled"],
                [admin_state, service_state, oper_state],
            )
        ):
            return Status.UNKNOWN
        if restart_required:
            return Status.UNKNOWN
        return Status.OK

    @staticmethod
    async def get_cells(pool: Any, device_id=None) -> ChainMap:
        """
        Retrieve cell information from the database survey reports.

        Args:
            pool: Database connection pool.

        Returns:
            A ChainMap containing survey report data for each cell.
        """
        async with pool.acquire() as connection:
            query = Survey(device_id=device_id, report_type="ENodeBReport")
            sql, args = pool.render(query.sql_select(device_id=device_id), **query._values)
            rows = await connection.fetch(sql, *args)
            surveys = [
                Survey(
                    **{
                        **dict(row),
                        "report": json.loads(row.get("report", "{}")),
                    }
                )
                for row in rows
            ]
            return ChainMap(*[ENodeBReport.items(survey.report) for survey in surveys])

    @staticmethod
    def payload(alarm: AlarmEntry, extra_info: str = "") -> dict:
        """
        Construct a payload dictionary from an AlarmEntry for publishing.

        Args:
            alarm: The AlarmEntry instance to convert into a payload.
            extra_info: Additional information to include in the payload (optional).

        Returns:
            A dictionary representing the alarm event payload.
        """

        if alarm.cleared:
            trend = AlarmStatus.resolved.value
        else:
            if alarm.updated_at and alarm.updated_at != alarm.created_at:
                trend = AlarmStatus.updated.value
            else:
                trend = AlarmStatus.new.value

        return {
            "header": {
                "domain": "nms",
                "eventId": str(alarm.event_id),
                "eventName": alarm.message,
                "eventType": "alarm",
                "priority": "medium",
                "reportingEntityName": "druid_interactions",
                "sourceName": alarm.component,
                "sourceId": str(alarm.alarm_id),
                "eventTime": alarm.updated_at,
                "systemDN": alarm.device_id,
            },
            "data": {
                "objectId": alarm.device_id,
                "objectType": "DRUID",
                "streetCellId": "",
                "uri": alarm.component,
                "type": alarm.message,
                "cause": alarm.description or alarm.message,
                "perceivedSeverity": alarm.severity,
                "specificProblem": alarm.message,
                "trendIndication": trend,
                "monitoredAttributes": [],
                "proposedRepairActions": [],
                "additionalText": "",
                "additionalInformation": extra_info,
            },
        }

    @staticmethod
    def alarm_entries(
        instance: Instance, report_data: dict[type, list]
    ) -> Generator[AlarmEntry, None, None]:
        """
        Generate AlarmEntry instances from report data.

        Args:
            instance: The Druid Instance being processed.
            report_data: Dictionary containing data from various report queries.

        Yields:
            AlarmEntry instances created from the report data.
        """
        for item in report_data.get(AlarmsQuery, []):
            if not item:
                continue

            try:
                severity = Severity[item.get("severity", "").upper()]
            except (AttributeError, KeyError):
                severity = Severity.WARNING

            component = f"{item.get('obj_class', '')}/?id={item.get('obj_id', 0)}"
            identifier = item.get("extra_info", {}).get("identity") or item.get("obj_id")

            yield AlarmEntry(
                device_id=instance.name,
                alarm_type=AlarmType.INSTANCE_ALARM,
                created_at=item.get("start_time"),
                component=component,
                event_type=item.get("event_type"),
                obj_id=identifier,
                severity=severity,
                message=item.get("probable_cause"),
                cleared=item.get("acknowledged", False),
                alarm_id=item.get("id"),
                description=item.get("add_text"),
            )

    @staticmethod
    def reconcile_alarms(
        db_alarms: dict[str, AlarmEntry], new_alarms: list[AlarmEntry]
    ) -> dict[str, list[AlarmEntry]]:
        """
        Reconcile existing alarms with newly detected alarms and categorize them.

        Args:
            db_alarms: Existing alarms from database, mapped by unique key.
            new_alarms: New alarm entries from polling.

        Returns:
            Dictionary with lists of alarms categorized as:
            - NEW: Alarms that don't exist in the database
            - UPDATED: Alarms that exist but have changed properties
            - CLEARED: Alarms that exist in DB but not in new data
            - MATCH: Alarms that exist in both with no changes
        """
        result = {"NEW": [], "UPDATED": [], "CLEARED": [], "MATCH": []}

        # Create a map of new alarms by their ID
        new_alarm_map = {}
        for alarm in new_alarms:
            new_alarm_map[f"{alarm.alarm_type}_{alarm.alarm_id}"] = alarm

        # Check existing alarms - mark as CLEARED if not in new alarms
        for key, existing_alarm in db_alarms.items():
            if key not in new_alarm_map:
                existing_alarm.cleared = True
                result["CLEARED"].append(existing_alarm)
                continue

            # Alarm exists in both DB and new data - check if it needs updating
            new_alarm = new_alarm_map[key]
            if existing_alarm.description != new_alarm.description:
                # Update existing alarm with new properties
                existing_alarm.severity = new_alarm.severity
                existing_alarm.updated_at = datetime.datetime.now(tz=datetime.timezone.utc)
                existing_alarm.description = new_alarm.description
                result["UPDATED"].append(existing_alarm)
            else:
                # No changes needed
                result["MATCH"].append(new_alarm)

            # Remove from new_alarm_map to track what's left
            new_alarm_map.pop(key)

        # The remaining alarms in new_alarm_map are NEW
        for new_alarm in new_alarm_map.values():
            new_alarm.published_at = datetime.datetime.now(tz=datetime.timezone.utc)
            result["NEW"].append(new_alarm)

        return result

    @staticmethod
    def update_alarms(
        db_alarms: dict[str, AlarmEntry], new_alarms: list[AlarmEntry]
    ) -> list[AlarmEntry]:
        """
        Alias for reconcile_alarms that returns all updated alarms as a flat list.

        Args:
            db_alarms: Existing alarms from database, mapped by unique key.
            new_alarms: New alarm entries from polling.

        Returns:
            list of all alarms (NEW, UPDATED, CLEARED, MATCH combined)
        """
        result = Watchdog.reconcile_alarms(db_alarms, new_alarms)
        # Return all alarms as a flat list
        return result["NEW"] + result["UPDATED"] + result["CLEARED"] + result["MATCH"]

    def publish_entry(self, entry: AlarmEntry) -> Optional[AlarmEntry]:
        """
        Publish an alarm entry to the PubSub system.

        Args:
            entry: The AlarmEntry to publish.

        Returns:
            The published AlarmEntry if successful, or None if failed.
        """
        try:
            payload = self.payload(entry)
            self.logger.info(f"Publishing payload: {payload}")
            self.pubsub.set_topic("nms-alarms")
            self.pubsub.push_payload(payload)
            return entry
        except Exception as e:
            self.logger.warning(f"Failed to publish entry: {e}")
            return None

    async def get_instances(self, connection: Any) -> list[Instance]:
        """
        Retrieve Druid instances from the database.

        Args:
            connection: Database connection to use for the query.

        Returns:
            A list of Instance objects representing Druid instances.
        """
        sql, args = self.pool.render(Instance(None, None).sql_select())
        rows = await connection.fetch(sql, *args)
        return [Instance(**dict(row)) for row in rows]

    @staticmethod
    def generate_offline_alert_alarm_entry(instance: Instance) -> AlarmEntry:
        """
        Generate a watchdog alert alarm for a specific Druid instance.
        """
        return AlarmEntry(
            device_id=instance.name,
            alarm_type=AlarmType.OFFLINE_ALERT,
            component="watchdog",
            severity=Severity.MAJOR,
            message="Unable to contact Druid instance",
            description="Unable to contact Druid instance",
            event_id=uuid.uuid4(),
        )

    async def get_alarms_from_druid_instance(
        self, instance: Instance, connection
    ) -> list[AlarmEntry]:
        """
        Fetch and process data for a specific Druid instance.

        Args:
            instance: The Druid Instance to fetch data for.

        Returns:
            A list of AlarmEntry instances derived from the instance data.
        """

        self.logger.info(f"Accessing {instance.name}...")
        creds = await get_credentials(self.config, secret_id=instance.secret_id)
        report = StatusReport()
        try:
            data = await report.get_report_data(
                instance, clients=self.clients, creds=creds, verify=False
            )
            return list(self.alarm_entries(instance, data))

        except Exception as e:
            self.logger.warning(f"Error getting alarms for {instance.name}: {e}")
            watchdog_alert_alarm = await self.get_latest_watchdog_alert_alarm_from_db(
                instance, connection
            )
            if not watchdog_alert_alarm:
                watchdog_alert_alarm = self.generate_offline_alert_alarm_entry(
                    instance=instance
                )
                await self.sync_alarm_with_db(
                    entry=watchdog_alert_alarm, connection=connection, operation="INSERT"
                )
                self.logger.info(f"Inserted alarm entry: {watchdog_alert_alarm}")
            elif not watchdog_alert_alarm.alarm_id:
                await self.sync_alarm_with_db(
                    entry=watchdog_alert_alarm, connection=connection, operation="UPDATE"
                )
                self.logger.info(f"Updated alarm entry: {watchdog_alert_alarm}")
            raise e

    async def get_latest_watchdog_alert_alarm_from_db(
        self, instance: Instance, connection: Any
    ) -> Optional[AlarmEntry]:
        """
        Fetch latest watchdog alert alarm for a specific Druid instance from the database.

        Args:
            instance: The Druid Instance to check.
            connection: Database connection to use.

        Returns:
            The most recent watchdog alert alarm for the instance, if any.
            Orders by created_at timestamp descending and returns only the latest one.
        """
        query = self.generate_offline_alert_alarm_entry(instance=instance)
        sql, args = self.pool.render(
            query.sql_select_latest_watchdog_alert(device_id=instance.name), **query._values
        )
        rows = await connection.fetch(sql, *args)
        return self.get_alarm_entry(dict(rows[0])) if rows else None

    async def get_instance_alarms_from_db(
        self, connection: Any, instance: Instance
    ) -> dict[str, AlarmEntry]:
        """
        Fetch existing alarms for a specific Druid instance from the database.

        Args:
            connection: Database connection to use for the query.
            instance: The Druid Instance to retrieve alarms for.

        Returns:
            A dictionary mapping alarm keys to AlarmEntry instances.
            Keys are in format: "{alarm_type}_{alarm_id}" for alarms with alarm_id,
            or "{alarm_type}_{event_id}" for alarms without alarm_id (like OFFLINE_ALERT).
        """

        # Get all alarms for this device (not just INSTANCE_ALARM)
        sql = "SELECT * FROM druid_interactions.alarms WHERE device_id = $1 AND cleared = false AND alarm_type = 'instance alarm'"
        rows = await connection.fetch(sql, instance.name)

        result = {}
        for row in rows:
            alarm_entry = self.get_alarm_entry(dict(row))
            # Create a unique key for each alarm
            key = f"{alarm_entry.alarm_type}_{alarm_entry.alarm_id}"
            result[key] = alarm_entry

        return result

    async def sync_alarm_with_db(
        self, entry: AlarmEntry, connection: Any, operation: str
    ) -> AlarmEntry:
        """Process and publish a single alarm entry, updating the database if needed."""
        self.logger.info(f"Handling alarm entry: {entry}")

        if operation == "DELETE":
            sql = """
                DELETE FROM druid_interactions.alarms
                WHERE event_id = $1
                RETURNING *
            """
            args = [entry.event_id]
            db_action = "Deleted"
        elif operation == "UPDATE":
            sql = """
                UPDATE druid_interactions.alarms
                SET severity = $1,
                    message = $2,
                    updated_at = $3,
                    cleared = $4,
                    description = $5,
                    alarm_id = $6,
                    published_at = $7
                WHERE event_id = $8
                RETURNING *
            """
            args = [
                entry.severity,
                entry.message,
                datetime.datetime.now(datetime.timezone.utc),
                entry.cleared,
                entry.description or "",
                entry.alarm_id,
                entry.published_at,
                entry.event_id,
            ]
            db_action = "Updated"
        else:  # INSERT
            columns = [
                "device_id",
                "alarm_type",
                "created_at",
                "component",
                "event_id",
                "event_type",
                "obj_id",
                "severity",
                "qualifier",
                "message",
                "cleared",
                "updated_at",
                "published_at",
                "description",
            ]
            values = [
                entry.device_id,
                entry.alarm_type,
                entry.created_at,
                entry.component,
                entry.event_id,
                entry.event_type,
                entry.obj_id,
                entry.severity,
                entry.qualifier,
                entry.message,
                entry.cleared,
                entry.updated_at,
                entry.published_at,
                entry.description,
            ]
            if entry.alarm_type == AlarmType.INSTANCE_ALARM:
                columns.append("alarm_id")
                values.append(entry.alarm_id)

            placeholders = [f"${i + 1}" for i in range(len(columns))]

            # Handle conflict resolution based on whether alarm_id is present
            if entry.alarm_type == AlarmType.INSTANCE_ALARM and entry.alarm_id is not None:
                # For INSTANCE_ALARM with alarm_id, use the standard conflict resolution
                conflict_clause = """
                    ON CONFLICT (device_id, alarm_type, alarm_id)
                    WHERE alarm_id IS NOT NULL
                    DO UPDATE SET
                        severity = EXCLUDED.severity,
                        message = EXCLUDED.message,
                        updated_at = EXCLUDED.updated_at,
                        cleared = EXCLUDED.cleared
                """
            else:
                # For other alarm types (like OFFLINE_ALERT) or when alarm_id is NULL,
                # use event_id as the unique identifier
                conflict_clause = """
                    ON CONFLICT (event_id) DO UPDATE SET
                        severity = EXCLUDED.severity,
                        message = EXCLUDED.message,
                        updated_at = EXCLUDED.updated_at,
                        cleared = EXCLUDED.cleared
                """

            sql = f"""
                INSERT INTO druid_interactions.alarms
                ({", ".join(columns)})
                VALUES ({", ".join(placeholders)})
                {conflict_clause}
                RETURNING *
            """
            args = values
            db_action = "Inserted"

        # Execute with transaction control
        tr = connection.transaction()
        await tr.start()
        try:
            self.logger.info(f"Executing {db_action} SQL: {sql}, with args: {args}")
            result = await connection.execute(sql, *args)
            self.logger.info(f"{db_action} result: {result}")
            await tr.commit()
            self.logger.info(f"alarm: [ {entry.event_id} ], {db_action} successfully")
        except Exception as e:
            await tr.rollback()
            self.logger.error(f"Failed to {db_action.lower()} alarm: {e}")
            raise

        return entry

    async def update_instance_status(
        self, instance: Instance, new_status: Status, connection: Any
    ):
        """
        Update the status of a Druid instance in the database.

        Args:
            instance: The Druid Instance to update.
            new_status: The new status for the instance.
            connection: Database connection to use for updating the status.
        """

        # Only update the status if it has changed
        if instance.status != new_status:
            self.logger.info(f"Updating status for {instance.name}: {new_status}")
            instance.status = new_status
            sql, args = self.pool.render(
                instance.sql_update(name=instance.name), **instance._values
            )
            await connection.execute(sql, *args)
            self.logger.info(f"Updated status for {instance.name}: {new_status}")

    async def handle_status_update(
        self,
        instance: Instance,
        new_status: Status,
        old_status: Status,
    ):
        """
        Handle and publish status updates for a Druid instance.

        Args:
            instance: The Druid Instance being processed.
            alarms: Dictionary of the current alarms for this instance.
            old_status: The previous status of the instance.

        Publishes a status event if there's a change in status.
        """
        self.logger.info(f"Handling status update for {instance.name}")

        if new_status != old_status:
            status_event = StatusEvent(
                component_type="DRUID",
                component_id=instance.name,
                manager=None,
                event_timestamp=datetime.datetime.now(tz=datetime.timezone.utc),
                status=new_status,
                last_status=old_status,
            )
            self.logger.info(f"Publishing status event: {status_event}")
            try:
                self.pubsub.set_topic("nms-device-status")
                self.pubsub.push_payload(status_event.model_dump())
            except Exception as e:
                self.logger.warning(f"Failed to publish status event: {e}")

    def refresh_values(self, alarm_entry: AlarmEntry):
        """Refresh the _values dictionary with current field values."""
        return {
            "device_id": alarm_entry.device_id,
            "alarm_type": alarm_entry.alarm_type,
            "created_at": alarm_entry.created_at,
            "component": alarm_entry.component,
            "event_id": alarm_entry.event_id,
            "event_type": alarm_entry.event_type,
            "obj_id": alarm_entry.obj_id,
            "severity": alarm_entry.severity,
            "qualifier": alarm_entry.qualifier,
            "message": alarm_entry.message,
            "cleared": alarm_entry.cleared,
            "updated_at": alarm_entry.updated_at,
            "published_at": alarm_entry.published_at,
            "alarm_id": alarm_entry.alarm_id,
            "description": alarm_entry.description,
        }

    async def clear_offline_alert_alarm(self, connection: Any, db_alarms: Any):
        """Handle offline alert alarms for a specific Druid instance."""
        # Clear AlarmType.OFFLINE_ALERT alarm if found
        offline_alert_alarm: AlarmEntry | None = next(
            (alarm for alarm in db_alarms if alarm.alarm_type == AlarmType.OFFLINE_ALERT), None
        )
        if offline_alert_alarm:
            self.logger.info(f"Clearing offline alert alarm: {offline_alert_alarm}")
            current_time = datetime.datetime.now(tz=datetime.timezone.utc)
            offline_alert_alarm.cleared = True
            offline_alert_alarm.updated_at = current_time
            offline_alert_alarm.published_at = current_time

            # Force refresh of values as for some weired reason it's not picking up the  updated values
            await self.sync_alarm_with_db(offline_alert_alarm, connection, operation="UPDATE")
            self.logger.info(f"Cleared offline alert alarm: {offline_alert_alarm}")

            # Publish the alarm status to PubSub
            self.publish_entry(offline_alert_alarm)

    def _get_licence_new_expiry_type(self, alarm: AlarmEntry) -> LicenceExpiryType:
        """
        Determine the license expiry type from an alarm entry.

        Args:
            alarm: The alarm entry to check.

        Returns:
            The license expiry type (EXPIRED or EXPIRING).
        """
        if alarm.message == LicenceExpiryType.LICENCE_EXPIRED.value:
            return LicenceExpiryType.LICENCE_EXPIRED
        return LicenceExpiryType.LICENCE_EXPIRING

    async def get_latest_licence_expiry_alarm_from_db(
        self, instance: Instance, connection: Any
    ) -> Optional[AlarmEntry]:
        """
        Fetch the latest license expiry alarm for a specific Druid instance from the database.

        Args:
            instance: The Druid Instance to check.
            connection: Database connection to use.

        Returns:
            The most recent license expiry alarm for the instance, if any.
        """
        query = AlarmEntry(device_id=instance.name, alarm_type=AlarmType.LICENCE_EXPIRY)
        sql, args = self.pool.render(
            f"{query.sql_select(device_id=instance.name, order_by=True)} LIMIT 1",
            **query._values,
        )
        rows = await connection.fetch(sql, *args)
        return self.get_alarm_entry(dict(rows[0])) if rows else None

    def generate_licence_expiry_alarm_entry(
        self, instance: Instance, severity: Severity, message: str, description: str
    ) -> AlarmEntry:
        """
        Generate a license expiry alarm for a specific Druid instance.

        Args:
            instance: The Druid Instance to create an alarm for.
            severity: The severity level of the alarm.
            message: The message for the alarm (typically the license expiry type).
            description: A description of the alarm (e.g., "License will expire in 5 days").

        Returns:
            A new AlarmEntry for the license expiry.
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        return AlarmEntry(
            device_id=instance.name,
            alarm_type=AlarmType.LICENCE_EXPIRY,
            component="licence",
            severity=severity,
            message=message,
            description=description,
            created_at=now,
            published_at=now,
            updated_at=now,
        )

    async def update_licence_expiry_alarm(
        self,
        alarm: AlarmEntry,
        severity: Severity,
        message: str,
        description: str,
        connection: Any,
    ) -> AlarmEntry:
        """
        Update an existing license expiry alarm.

        Args:
            alarm: The existing alarm to update.
            severity: The new severity level.
            message: The new message.
            description: The new description.
            connection: Database connection to use.

        Returns:
            The updated AlarmEntry.
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        alarm.severity = severity
        alarm.message = message
        alarm.description = description
        alarm.updated_at = now
        alarm.published_at = now

        await self.sync_alarm_with_db(alarm, connection, operation="UPDATE")
        self.publish_entry(alarm)
        return alarm

    async def clear_licence_expiry_alarm(
        self, alarm: AlarmEntry, connection: Any
    ) -> AlarmEntry:
        """
        Mark a license expiry alarm as cleared.

        Args:
            alarm: The alarm to clear.
            connection: Database connection to use.

        Returns:
            The cleared AlarmEntry.
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        alarm.cleared = True
        alarm.updated_at = now
        alarm.published_at = now

        await self.sync_alarm_with_db(alarm, connection, operation="UPDATE")
        self.publish_entry(alarm)
        return alarm

    async def _get_license_data(self, instance: Instance) -> dict:
        """
        Get license data from a Druid instance.

        Args:
            instance: The Druid Instance to get data from.

        Returns:
            A dictionary containing license data.
        """
        report = StatusReport()
        creds = await get_credentials(self.config, secret_id=instance.secret_id)
        return await report.get_report_data(
            instance, clients=self.clients, creds=creds, verify=False
        )

    async def _process_no_expiry(
        self, instance: Instance, existing_alarm: Optional[AlarmEntry], connection: Any
    ) -> None:
        """
        Process the case where there is no license expiry issue.

        Args:
            instance: The Druid Instance being processed.
            existing_alarm: Any existing license expiry alarm.
            connection: Database connection to use.
        """
        if existing_alarm and not existing_alarm.cleared:
            self.logger.info(f"Clearing license expiry alarm for {instance.name}")
            await self.clear_licence_expiry_alarm(existing_alarm, connection)

    async def _process_changed_expiry_type_alarm(
        self,
        instance: Instance,
        existing_alarm: AlarmEntry,
        severity: Severity,
        new_expiry_type: LicenceExpiryType,
        description: str,
        connection: Any,
    ) -> None:
        """
        Process the transition from license expiring to expired.

        Args:
            instance: The Druid Instance being processed.
            existing_alarm: The existing license expiry alarm.
            severity: The severity level of the new alarm.
            new_expiry_type: The type of license expiry.
            description: A description of the alarm.
            connection: Database connection to use.
        """
        self.logger.info(f"License for {instance.name} has expired, updating alarm")
        # Clear the expiring alarm
        await self.clear_licence_expiry_alarm(existing_alarm, connection)
        # Create a new expired alarm
        new_alarm = self.generate_licence_expiry_alarm_entry(
            instance=instance,
            severity=severity,
            message=new_expiry_type.value,
            description=description,
        )
        await self.sync_alarm_with_db(new_alarm, connection, operation="INSERT")
        self.publish_entry(new_alarm)

    async def _create_new_expiry_alarm(
        self,
        instance: Instance,
        severity: Severity,
        new_expiry_type: LicenceExpiryType,
        description: str,
        connection: Any,
    ) -> None:
        """
        Create a new license expiry alarm.

        Args:
            instance: The Druid Instance to create an alarm for.
            severity: The severity level of the alarm.
            new_expiry_type: The type of license expiry.
            description: A description of the alarm.
            connection: Database connection to use.
        """
        self.logger.info(f"Creating new license expiry alarm for {instance.name}")
        new_alarm = self.generate_licence_expiry_alarm_entry(
            instance=instance,
            severity=severity,
            message=new_expiry_type.value,
            description=description,
        )
        await self.sync_alarm_with_db(new_alarm, connection, operation="INSERT")
        self.publish_entry(new_alarm)

    async def handle_licence_expiry_alarm(self, connection: Any, instance: Instance):
        """
        Handle license expiry alarms for a specific Druid instance.

        This function checks the license status of a Druid instance and creates,
        updates, or clears license expiry alarms as needed.

        Args:
            connection: Database connection to use for alarm operations.
            instance: The Druid Instance to check for license expiry.
        """
        self.logger.info(f"Checking license expiry for {instance.name}")

        try:
            # Get license data and existing alarm
            data = await self._get_license_data(instance)
            existing_alarm = await self.get_latest_licence_expiry_alarm_from_db(
                instance, connection
            )

            # Process license data
            for item in data.get(FeaturesQuery, []):
                # Get expiry information
                expiry_info = StatusReport.check_expiry(item)
                severity = expiry_info.get("severity")
                description = expiry_info.get("description")
                new_expiry_type = expiry_info.get("licence_expiry_type")

                # Handle based on severity
                if severity is None or severity == Severity.NONE:
                    # No license expiry issue
                    await self._process_no_expiry(instance, existing_alarm, connection)
                elif existing_alarm:
                    if existing_alarm.cleared:
                        self.logger.info(
                            "Existing Licence expiry alarm is cleared, creating a new one."
                        )
                        # Create a new alarm
                        await self._create_new_expiry_alarm(
                            instance, severity, new_expiry_type, description, connection
                        )
                    else:
                        # Handle existing alarm
                        current_expiry_type = self._get_licence_new_expiry_type(existing_alarm)
                        # Check for transition from expiring to expired or vice versa
                        if new_expiry_type != current_expiry_type:
                            await self._process_changed_expiry_type_alarm(
                                instance,
                                existing_alarm,
                                severity,
                                new_expiry_type,
                                description,
                                connection,
                            )

                        # Check for description change
                        elif existing_alarm.description != description:
                            self.logger.info(
                                f"Updating license expiry alarm for {instance.name}"
                            )
                            await self.update_licence_expiry_alarm(
                                alarm=existing_alarm,
                                severity=severity,
                                message=new_expiry_type.value,
                                description=description,
                                connection=connection,
                            )
                else:
                    self.logger.info("No existing licence expiry alarm, creating a new one.")
                    # Create a new alarm
                    await self._create_new_expiry_alarm(
                        instance, severity, new_expiry_type, description, connection
                    )
        except Exception as e:
            self.logger.error(f"Error handling license expiry for {instance.name}: {e}")
            # Don't raise the exception - let the process continue

    async def process_instance(self, instance: Instance, connection: Any):
        """
        Process the alarms and updated status for a single Druid instance.

        Args:
            instance: The Druid Instance to process.
            connection: Database connection to use for operations involving alarm updates.

        This method calls the relevant handlers to manage alarms and status changes.
        """

        async with connection.transaction():
            try:
                new_alarms = await self.get_alarms_from_druid_instance(instance, connection)
            except Exception:
                return

            db_alarms = await self.get_instance_alarms_from_db(connection, instance)
            self.logger.info(f"Instance: [ {instance.name} ], DB alarms: {db_alarms}")

            await self.clear_offline_alert_alarm(connection, list(db_alarms.values()))
            await self.handle_licence_expiry_alarm(connection, instance)

            # Reconcile alarms and categorize them
            reconciled_alarms = self.reconcile_alarms(db_alarms, new_alarms)
            self.logger.info(
                f"Instance: [ {instance.name} ], Reconciled alarms: NEW={len(reconciled_alarms['NEW'])}, "
                f"UPDATED={len(reconciled_alarms['UPDATED'])}, "
                f"CLEARED={len(reconciled_alarms['CLEARED'])}, "
                f"MATCH={len(reconciled_alarms['MATCH'])}"
            )

            # Process each alarm category
            for alarm in reconciled_alarms["NEW"]:
                entry = await self.sync_alarm_with_db(alarm, connection, operation="INSERT")
                self.publish_entry(entry)

            for alarm in reconciled_alarms["UPDATED"]:
                entry = await self.sync_alarm_with_db(alarm, connection, operation="UPDATE")
                self.publish_entry(entry)

            for alarm in reconciled_alarms["CLEARED"]:
                entry = await self.sync_alarm_with_db(alarm, connection, operation="DELETE")
                self.publish_entry(entry)

            current_alarms = []
            for alarm_list in [
                reconciled_alarms["NEW"],
                reconciled_alarms["UPDATED"],
                reconciled_alarms["MATCH"],
            ]:
                for alarm in alarm_list:
                    current_alarms.append(alarm)

            # Check status based on all non-cleared alarms
            # Filter out eNodeB alarms for status determination
            instance_filtered_alarms = [
                entry
                for entry in current_alarms
                if entry.alarm_type != AlarmType.INSTANCE_ALARM
            ]

            old_status = instance.status
            new_status = self.device_status(instance_filtered_alarms)
            await self.update_instance_status(instance, new_status, connection)
            await self.handle_status_update(instance, new_status, old_status)

            # Store updated alarms
            self.alarms[instance.name] = current_alarms

    async def setup_pubsub(self, loop: Optional[asyncio.AbstractEventLoop] = None) -> bool:
        """
        Set up the PubSub connection if it is not already established.

        Args:
            loop: Optional asyncio event loop to use for setting up the connection.

        Returns:
            True if PubSub connection is successfully established, False otherwise.
        """
        if self.pubsub is not None:
            return True

        try:
            self.pubsub = PubSub(config=self.config, loop=loop)
            self.pubsub.set_topic("nms-alarms")
            self.logger.info("PubSub connected.")
            return True
        except Exception as e:
            self.logger.warning(f"Unable to connect to PubSub service: {e}")
            return False

    async def run(
        self,
        limit: int = sys.maxsize,
        loop: Optional[asyncio.AbstractEventLoop] = None,
    ):
        """
        The main run loop for the Watchdog reporter that continuously monitors instances.

        Args:
            limit: Maximum number of iterations for the run loop (or infinity by default).
            loop: Optional asyncio event loop to use for the run loop.

        Continuously fetches, processes instances, and updates status until limit is exhausted.
        """
        self.logger.info("Starting Watchdog reporter...")
        self.pool = self.pool or await PoolFactory.create(self.config)
        self.logger.info(f"Using database pool: {self.pool}")

        while limit:
            if limit % 5 == 0:
                self.logger.info("Generating reports...")

            if not await self.setup_pubsub(loop):
                await asyncio.sleep(self.interval)
                continue

            try:
                async with self.pool.acquire() as connection:
                    instances = await self.get_instances(connection)
                    for instance in instances:
                        if instance.name == "dauk-mrl-ruby-druid-nhe1":
                            continue
                        await self.process_instance(instance, connection)

            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")

            limit -= 1
            await asyncio.sleep(self.interval)
