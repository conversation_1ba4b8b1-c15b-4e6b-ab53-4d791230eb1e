import json
import logging
import urllib.parse
from uuid import UUID

import httpx
from da_common.security import get_current_user
from druid_interactions.controller import get_alarms
from druid_interactions.creds import get_credentials
from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.models.instance import Instance
from druid_interactions.models.instance import InstanceData
from druid_interactions.models.report import ReportData
from druid_interactions.queries.aspects import AspectsReport
from druid_interactions.queries.entities import EntitiesReport
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.queries.features import LicenceReport
from druid_interactions.queries.networks import NetworksReport
from druid_interactions.queries.segw import SeGWReport
from druid_interactions.queries.sessions import SessionsReport
from druid_interactions.queries.system import SystemQuery
from druid_interactions.watchdog import Watchdog
from fastapi import APIRouter
from fastapi import HTTPException
from fastapi import Request
from fastapi import Security
from fastapi import status as http_status
from fastapi.encoders import jsonable_encoder
from starlette.responses import JSONResponse


instance_router = APIRouter(
    prefix="/nms/druid/instances",
    tags=["Instances"],
    dependencies=[Security(get_current_user)],
)


features_router = APIRouter(
    prefix="/nms/druid",
    tags=["Features"],
    dependencies=[Security(get_current_user)],
)


reports_router = APIRouter(
    prefix="/nms/druid/reports",
    tags=["Reports"],
    dependencies=[Security(get_current_user)],
)


alarms_router = APIRouter(
    prefix="/nms/druid/alarms",
    tags=["Alarms"],
    dependencies=[Security(get_current_user)],
)


enodebs_router = APIRouter(
    prefix="/nms/druid/enodebs",
    tags=["ENodeBs"],
    dependencies=[Security(get_current_user)],
)


system_router = APIRouter(
    prefix="/nms/druid",
    tags=["System"],
    dependencies=[Security(get_current_user)],
)


@features_router.get(
    "/{instance_name}/features/licence",
    status_code=http_status.HTTP_200_OK,
)
async def get_licence(
    request: Request,
    instance_name: str,
):
    "Get licence information"
    logger = logging.getLogger("druid_interactions.get_licence")
    reply = {}
    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            logger.info(f"Instance query: {sql=} {args=}")
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )

    try:
        logger.info(f"Getting credentials for {instance_name}...")
        creds = await get_credentials(request.app.state.config, secret_id=instance.secret_id)
        username = creds.get("username", "")
        password = creds.get("password", "")

        query = FeaturesQuery()

        client = query.client(
            instance,
            request.app.state.clients,
            username=username,
            password=password,
            verify=False,
        )
    except Exception as e:
        return JSONResponse(
            content=dict(detail=f"No secret for Druid {instance_name} ({e!s})"),
            status_code=http_status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    try:
        logger.info(f"Connecting to Druid as {username}...")
        response = await query.get_object(client, params=None)

        reply = response.json()
        data = [query.normalize_data(i) for i in reply]
        reply = data[0] if data else reply
    except (Exception, httpx.ConnectError) as e:
        return JSONResponse(
            content=dict(detail=f"Unable to connect to Druid {instance_name} ({e!s})"),
            status_code=http_status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    return reply


@reports_router.get("/{instance_name}", status_code=http_status.HTTP_200_OK)
async def get_report(
    request: Request,
    instance_name: str,
) -> ReportData:
    "Get full report on Druid instance"
    logger = logging.getLogger("druid_interactions.get_report")
    logger.info(f"Getting report for {instance_name}")
    if instance_name not in request.app.state.reporter.reports:
        logger.error(f"No Druid instance {instance_name}")
        raise HTTPException(
            status_code=http_status.HTTP_410_GONE,
            detail=f"No Druid instance {instance_name}",
        )

    try:
        cells = await Watchdog.get_cells(request.app.state.pool, device_id=instance_name)
    except json.JSONDecodeError:
        logger.warning("No eNodeB reports available.")
        cells = {}

    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )

    status = instance.status
    reports = request.app.state.reporter.reports

    return ReportData(
        device_id=instance_name,
        status=status,
        aspects=reports.get(instance_name, {}).get(AspectsReport, {}),
        details=reports.get(instance_name, {}).get(LicenceReport, {}),
        networks=reports.get(instance_name, {}).get(NetworksReport, []),
        segw=reports.get(instance_name, {}).get(SeGWReport, {}),
        sessions=reports.get(instance_name, {}).get(SessionsReport, []),
        enodebs=list(cells.values()),
    )


@reports_router.get("/{instance_name}/entities", status_code=http_status.HTTP_200_OK)
async def get_entities_report(
    request: Request,
    instance_name: str,
    response_model=None,
):
    "Get entities report"
    logger = logging.getLogger("druid_interactions.get_entities_report")
    logger.info(f"Getting Entities report for {instance_name}")
    reply = []
    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )
            reply = request.app.state.reporter.reports.get(instance.name, {}).get(
                EntitiesReport, []
            )

    return reply


@reports_router.get("/{instance_name}/networks", status_code=http_status.HTTP_200_OK)
async def get_networks_report(
    request: Request,
    instance_name: str,
):
    "Get networks report"
    logger = logging.getLogger("druid_interactions.get_networks_report")
    reply = []
    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )
            reply = request.app.state.reporter.reports.get(instance.name, {}).get(
                NetworksReport, []
            )
            logger.info(f"{request.app.state.reporter.reports=}")

    return reply


@reports_router.get("/{instance_name}/segw", status_code=http_status.HTTP_200_OK)
async def get_segw_report(
    request: Request,
    instance_name: str,
) -> dict:
    "Get segw report"
    logger = logging.getLogger("druid_interactions.get_segw_report")
    reply = []
    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )
            reply = request.app.state.reporter.reports.get(instance.name, {}).get(
                SeGWReport, {}
            )
            logger.info(f"{request.app.state.reporter.reports=}")

    return reply


@reports_router.get("/{instance_name}/sessions", status_code=http_status.HTTP_200_OK)
async def get_sessions_report(
    request: Request,
    instance_name: str,
) -> list:
    "Get sessions report"
    logger = logging.getLogger("druid_interactions.get_sessions_report")
    reply = []
    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )
            reply = request.app.state.reporter.reports.get(instance.name, {}).get(
                SessionsReport, []
            )
            logger.info(f"{request.app.state.reporter.reports=}")

    return reply


@alarms_router.get("/{instance_name}", status_code=http_status.HTTP_200_OK)
async def get_instance_alarms(
    request: Request,
    instance_name: str,
) -> list[AlarmEntry]:
    "Get instance alarms"

    rows = await get_alarms(request.app.state.pool, instance_name)
    return [AlarmEntry(**dict(row)) for row in rows]


@alarms_router.get("/{instance_name}/{event_id}", status_code=http_status.HTTP_200_OK)
async def get_instance_alarm_by_event_id(
    request: Request,
    instance_name: str,
    event_id: UUID,
) -> AlarmEntry | None:
    "Get instance alarm by event id"
    logger = logging.getLogger("druid_interactions.get_instance_alarm_by_event_id")
    logger.info(f"Getting alarm for event_id {event_id} on instance {instance_name}")

    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            try:
                query = AlarmEntry(device_id=instance_name)
                # Convert UUID to string and include it in the args
                sql, args = request.app.state.pool.render(
                    f"{query.sql_select(device_id=instance_name)} AND event_id = :event_id",
                    **{**query._values, "event_id": str(event_id)},
                )
                rows = await connection.fetch(sql, *args)
                if rows:
                    return AlarmEntry(**dict(rows[0]))
                else:
                    msg = f"No alarm event {event_id} on instance {instance_name}"
                    logger.error(msg)
                    return JSONResponse(
                        content=dict(detail=msg),
                        status_code=http_status.HTTP_410_GONE,
                    )
            except Exception as e:
                msg = (
                    f"Error getting alarm event {event_id} on instance {instance_name} ({e!s})"
                )
                logger.error(msg)
                return JSONResponse(
                    content=dict(detail=msg),
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                )


@enodebs_router.get("/{cell_id}", status_code=http_status.HTTP_200_OK)
async def get_enodebs_by_cell_id(
    request: Request,
    cell_id: int,
    response_model=None,
):
    "Get instance enodebs by cell id"
    try:
        cells = await Watchdog.get_cells(request.app.state.pool)
        return cells[cell_id]
    except json.JSONDecodeError:
        return JSONResponse(
            content=dict(detail="No eNodeB reports available."),
            status_code=http_status.HTTP_503_SERVICE_UNAVAILABLE,
        )
    except KeyError:
        return JSONResponse(
            content=dict(detail=f"No cell {cell_id} found."),
            status_code=http_status.HTTP_410_GONE,
        )


@system_router.get("/{instance_name}/system/status", status_code=http_status.HTTP_200_OK)
async def get_status(
    request: Request,
    instance_name: str,
    response_model=None,
):
    "Get status information"
    logger = logging.getLogger("druid_interactions.get_status")
    reply = {}
    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            logger.info(f"Query: {sql=} {args=}")
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )

            creds = await get_credentials(
                request.app.state.config, secret_id=instance.secret_id
            )
            username = creds.get("username", "")
            password = creds.get("password", "")

            query = SystemQuery()

            client = query.client(
                instance,
                request.app.state.clients,
                username=username,
                password=password,
                verify=False,
            )
            logger.info(f"Connecting to Druid as {username}...")
            try:
                response = await query.get_object(client, params=None)
            except httpx.ConnectError as e:
                return JSONResponse(
                    content=dict(detail=f"Unable to connect to Druid {instance_name} ({e!s})"),
                    status_code=http_status.HTTP_503_SERVICE_UNAVAILABLE,
                )

            data = response.json()
            schema = query.registry.get(response.url, {})
            reply = query.normalize_data(data, schema=schema)

    return reply


@system_router.post(
    "/{instance_name}/system/{managed_object}",
    status_code=http_status.HTTP_200_OK,
)
async def post_system_object_update(
    request: Request,
    instance_name: str,
    managed_object: str,
    update: dict,  # TODO: Constrain type
    response_model=None,
):
    "Make a change to an internal managed object"
    logger = logging.getLogger("druid_interactions.post_system_object_update")

    query = Instance(name=instance_name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_select(name=query.name), **query._values
            )
            logger.info(f"Query: {sql=} {args=}")
            rows = await connection.fetch(sql, *args)
            try:
                instance = Instance(**dict(rows[0]))
            except IndexError:
                return JSONResponse(
                    content=dict(detail=f"No Druid instance {instance_name}"),
                    status_code=http_status.HTTP_410_GONE,
                )

    creds = await get_credentials(request.app.state.config, secret_id=instance.secret_id)
    username = creds.get("username", "")
    password = creds.get("password", "")

    query = SystemQuery()

    client = query.client(
        instance,
        request.app.state.clients,
        username=username,
        password=password,
        verify=False,
    )

    try:
        admin_state = update.pop("admin_state")
        params = urllib.parse.urlencode(update)
    except KeyError:
        return JSONResponse(
            content=dict(detail=f"admin_state not found in {update}"),
            status_code=http_status.HTTP_400_BAD_REQUEST,
        )

    logger.info(f"Connecting to Druid as {username}...")
    url = f"/{managed_object}?{params}"
    data = dict(admin_state=admin_state)
    try:
        response = await client.patch(url, data=data)
    except httpx.ConnectError as e:
        return JSONResponse(
            content=dict(detail=f"Unable to connect to Druid {instance_name} ({e!s})"),
            status_code=http_status.HTTP_503_SERVICE_UNAVAILABLE,
        )

    if response.status_code == 200:
        return JSONResponse(
            content=dict(detail="Update successful."),
            status_code=response.status_code,
        )
    else:
        return JSONResponse(
            content=jsonable_encoder(dict(detail=response.content)),
            status_code=response.status_code,
        )


@instance_router.get("", status_code=http_status.HTTP_200_OK)
async def get_instances(request: Request) -> list[InstanceData]:
    "list all Instance records"
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(Instance(None, None).sql_select())
            rows = await connection.fetch(sql, *args)

    return [InstanceData(**dict(row)) for row in rows]


@instance_router.post("", status_code=http_status.HTTP_200_OK)
async def post_instance(
    request: Request,
    data: InstanceData,
    response_model=None,
):
    "Create an Instance record"
    logger = logging.getLogger("druid_interactions.post_instance")
    instance = Instance(**{k: v for k, v in data.model_dump().items() if v is not None})

    logger.info(f"{instance=}")
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(instance.sql_insert(), **instance._values)
            try:
                await connection.execute(sql, *args)
            except Exception as e:
                return JSONResponse(
                    content=dict(detail=f"Error while adding {instance} ({e!s})"),
                    status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

    return InstanceData(**instance._values)


@instance_router.delete("/{name}", status_code=http_status.HTTP_200_OK)
async def delete_instance(
    request: Request,
    name: str,
) -> InstanceData | None:
    "Delete Instance record"
    logger = logging.getLogger("druid_interactions.delete_instance")

    logger.info(f"{name=}")
    query = Instance(name=name, host=None)
    async with request.app.state.pool.acquire() as connection:
        async with connection.transaction():
            sql, args = request.app.state.pool.render(
                query.sql_delete(name=query.name), **query._values
            )
            rows = await connection.fetch(sql, *args)
            if rows:
                return InstanceData(**dict(rows[0]))
