import functools
import json
import os
import pathlib
import platform
import re
import sqlite3
from collections.abc import Coroutine
from contextlib import asynccontextmanager

import aiosqlite
import asyncpg
import buildpg
from da_common.config import Config
from druid_interactions.migrations.utils import OfflineConfig
from starlette.applications import Starlette


@asynccontextmanager
async def pool_manager(app: Starlette):
    app.state.pool = await PoolFactory.create(app.state.config)
    yield
    await app.state.pool.close()


class PoolFactory:
    """Presents a generic interface for database pools.

    Allows a local SQLite database to accept queries intended for Postgres via asyncpg.
    Conforms to API described here:
    https://magicstack.github.io/asyncpg/current/index.html

    """

    @staticmethod
    def get_config(path: pathlib.Path, env=None, config_class=Config):
        "Return a configuration suitable for the current environment"
        env = env or os.environ
        if env.get("ENVIRONMENT", "").lower() == "gcp":
            return config_class.get_config(path)
        else:
            return OfflineConfig.get_config(path)

    @staticmethod
    def get_connector(url: str, loop=None):
        return functools.partial(
            aiosqlite.connect,
            url,
            uri=True,
            detect_types=sqlite3.PARSE_COLNAMES | sqlite3.PARSE_DECLTYPES,
            loop=loop,
        )

    @staticmethod
    def build_to_sqlite(sql: str) -> str:
        "Allows buildpg-style Postgres statements to be used in SQLite"

        # Remove schema from foreign key constraint
        schema_regex = re.compile("(?<=REFERENCES\\\\s)([^\\\\.]+\\\\.)")
        sql = schema_regex.sub("", sql)

        # Substitute rich types with strings
        type_regex = re.compile("timestamptz|UUID")
        sql = type_regex.sub("TEXT", sql)

        # Remove JSON column default
        json_regex = re.compile("JSON[^,]*")
        sql = json_regex.sub("TEXT", sql)

        # Translate positional placeholder syntax
        values_regex = re.compile("(\\$\\d+)")
        sql = values_regex.sub("?", sql)

        # SQLite doesn't support 'not exists' on columns
        sql = sql.replace("COLUMN IF NOT EXISTS ", "COLUMN ")
        return sql

    @staticmethod
    def values_to_sqlite(data: dict) -> dict:
        "Convert rich-typed Postgres values to basic types for SQLite"

        def buildpg_values(obj):
            obj.values = tuple(
                transforms.get(type(v), str)(v) if v is not None else v for v in obj.values
            )
            return obj

        transforms = {
            dict: json.dumps,
            buildpg.Values: buildpg_values,
        }

        return {
            k: transforms.get(type(v), str)(v) if v is not None else v for k, v in data.items()
        }

    @staticmethod
    def dict_record(cursor, row):
        fields = [column[0] for column in cursor.description]
        return {key: value for key, value in zip(fields, row)}

    @classmethod
    async def create(cls, config, *args, scheme="postgres", loop=None, **kwargs) -> Coroutine:
        dsn = config.get_db_url(scheme)
        pool = None
        if config.db_config_valid and scheme == "postgres":
            pool = await asyncpg.create_pool(dsn, loop=loop, **kwargs)

        db_name = config.data.get("db", {}).get("database", "")
        return cls(dsn, db_name=db_name, pool=pool, loop=loop, **kwargs)

    def __init__(self, dsn: str, db_name="", pool=None, loop=None, **kwargs):
        self.dsn = dsn
        self.db_name = db_name
        self.pool = pool
        self.loop = loop

    @property
    def backend(self):
        if self.pool:
            return asyncpg
        return aiosqlite

    def __await__(self):
        return self
        yield

    def acquire(self, *args, **kwargs):
        if self.pool:
            return self.pool.acquire(*args, **kwargs)
        return self

    def transaction(self, *args, **kwargs):
        return self

    async def close(self, *args, **kwargs):
        if self.pool:
            await self.pool.close()
        return self

    async def __aenter__(self, *args, **kwargs):
        return self

    async def __aexit__(self, exc_type, exc, tb):
        return False

    @staticmethod
    async def set_session(connection, dsn: str, db_name: str):
        if "memory" in dsn:
            db_file = ":memory:"
        elif platform.system() == "Windows":
            db_file = dsn.removeprefix("file:///")
        else:
            db_file = dsn.removeprefix("file://")
        await connection.execute("PRAGMA locking_mode = NORMAL")
        await connection.execute("PRAGMA journal_mode = WAL")
        await connection.execute("PRAGMA synchronous = FULL")
        await connection.execute("PRAGMA foreign_keys = ON")
        await connection.execute(f"ATTACH DATABASE '{db_file}' AS {db_name}")
        await connection.commit()
        return connection

    async def release(self, connection, *, timeout=None):
        await connection.close()
        return self

    async def execute(self, pg_sql: str, *pg_values: tuple, timeout: float = None) -> str:
        connector = self.get_connector(self.dsn, loop=self.loop)
        async with connector() as db:
            db.row_factory = self.dict_record
            await self.set_session(db, self.dsn, self.db_name)
            cursor = await db.execute(pg_sql, pg_values)
            await db.commit()
            return cursor

    async def executemany(self, pg_sql: str, pg_values: list, *, timeout: float = None):
        connector = self.get_connector(self.dsn, loop=self.loop)
        async with connector() as db:
            db.row_factory = self.dict_record
            await self.set_session(db, self.dsn, self.db_name)
            cursor = await db.executemany(pg_sql, pg_values)
            await db.commit()
            return cursor

    async def fetch(self, pg_sql, *pg_values: tuple, timeout=None, record_class=None) -> list:
        connector = self.get_connector(self.dsn, loop=self.loop)
        async with connector() as db:
            db.row_factory = self.dict_record
            await self.set_session(db, self.dsn, self.db_name)
            cursor = await db.execute(pg_sql, pg_values)
            rows = await cursor.fetchall()
            await db.commit()
            return rows

    def render(self, query, **kwargs):
        if self.backend is aiosqlite:
            query = self.build_to_sqlite(query)
            kwargs = self.values_to_sqlite(kwargs)

        return buildpg.render(query, **kwargs)
