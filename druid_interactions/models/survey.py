import dataclasses
import datetime
import functools
from uuid import UUID
from uuid import uuid4

from druid_interactions.types import SQLTable


# fmt: off
@dataclasses.dataclass(order=True)
class Survey(SQLTable):
    device_id: str | None = None
    report_type: str = ""
    report: dict = dataclasses.field(default_factory=dict, compare=False)
    survey_id: UUID = dataclasses.field(default_factory=uuid4)
    updated_at: datetime.datetime = dataclasses.field(
        default_factory=functools.partial(datetime.datetime.now, tz=datetime.timezone.utc)
    )

    def sql_delete(self, survey_id: UUID) -> str:
        return f"DELETE FROM {self.schema}.surveys WHERE survey_id = :survey_id RETURNING *"

    def sql_insert(self) -> str:
        return (
            f"INSERT INTO {self.schema}.surveys (:values__names) VALUES :values "
            "ON CONFLICT (device_id, report_type) DO UPDATE SET "
            "survey_id = excluded.survey_id, "
            "report = excluded.report, "
            "updated_at = excluded.updated_at "
        )

    def sql_select(
        self,
        device_id: str | None = None,
        report_type: str | None = None,
    ) -> str:
        if device_id and report_type:
            return (
                f"SELECT * FROM {self.schema}.surveys WHERE "
                "device_id = :device_id AND report_type = :report_type"
            )
        elif device_id:
            return f"SELECT * FROM {self.schema}.surveys WHERE device_id = :device_id"
        elif report_type:
            return f"SELECT * FROM {self.schema}.surveys WHERE report_type = :report_type"
        else:
            return f"SELECT * FROM {self.schema}.surveys"
