import dataclasses
import datetime
import functools
import textwrap

from druid_interactions.types import SQLTable


# fmt: off
@dataclasses.dataclass(eq=True, kw_only=True, unsafe_hash=True)
class Migration(SQLTable):
    created_at: datetime.datetime = dataclasses.field(
        default_factory=functools.partial(
            datetime.datetime.now, tz=datetime.timezone.utc
        )
    )
    version: list[int] = dataclasses.field(default_factory=list)
    note: str = ""

    def sql_select(self) -> str:
        return f"SELECT * FROM {self.schema}.migrations ORDER BY created_at DESC LIMIT 1"

    def sql_insert(self) -> str:
        return textwrap.dedent(f"""
        INSERT INTO {self.schema}.migrations (:values__names)
        VALUES :values
        """)
