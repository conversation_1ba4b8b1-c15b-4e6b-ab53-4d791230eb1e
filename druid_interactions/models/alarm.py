import dataclasses
import datetime
import functools
import json
from uuid import UUI<PERSON>
from uuid import uuid4

import buildpg
from druid_interactions.types import AlarmType
from druid_interactions.types import Severity
from druid_interactions.types import SQLTable


# fmt: off
@dataclasses.dataclass(order=True)
class AlarmEntry(SQLTable):
    """Alarm entry model."""

    device_id: str
    alarm_id: int | None = None
    alarm_type: AlarmType = AlarmType.WATCHDOG_DEBUG
    created_at: datetime.datetime = dataclasses.field(
        default_factory=functools.partial(datetime.datetime.now, tz=datetime.timezone.utc)
    )
    component: str = ""
    event_id: UUID = dataclasses.field(default_factory=uuid4)
    event_type: str = ""
    obj_id: int | None = None
    severity: Severity = Severity.NONE
    qualifier: str = ""
    message: str = ""
    cleared: bool = False
    updated_at: datetime.datetime = dataclasses.field(
        default_factory=functools.partial(datetime.datetime.now, tz=datetime.timezone.utc)
    )
    published_at: datetime.datetime | None = None

    description: str | None = None

    @functools.cached_property
    def _values_for_insert(self) -> dict:
        """Return values for insert, excluding alarm_id if it's None."""
        if self.alarm_id is not None:
            # If alarm_id has a value, use normal values
            return self._values

        # Create a dictionary excluding alarm_id
        data = {field.name: getattr(self, field.name)
                for field in dataclasses.fields(self)
                if field.name != "alarm_id" and getattr(self, field.name) is not None}

        # Transform values if needed
        transforms = {
            dict: json.dumps,
        }

        def null(arg):
            return arg

        processed_data = {}
        for k, v in data.items():
            transform = transforms.get(type(v), null)
            processed_data[k] = transform(getattr(v, "value", v))

        # Create the final dictionary with the buildpg.Values object
        return dict(processed_data, values=buildpg.Values(**processed_data))

    def sql_delete(self, event_id: UUID) -> str:
        return f"DELETE FROM {self.schema}.alarms WHERE event_id = :event_id RETURNING *"

    def sql_insert(self):
        """Generate SQL for inserting a new alarm.
        If alarm_id is None, it will be excluded from the INSERT to use the sequence.
        """
        columns = [
            "device_id",
            "alarm_type",
            "created_at",
            "component",
            "event_id",
            "event_type",
            "obj_id",
            "severity",
            "qualifier",
            "message",
            "cleared",
            "updated_at",
            "published_at",
            "alarm_id",
            "description",
        ]
        values = [
            self.device_id,
            self.alarm_type.value,
            self.created_at,
            self.component,
            self.event_id,
            self.event_type,
            self.obj_id,
            self.severity.value,
            self.qualifier,
            self.message,
            self.cleared,
            self.updated_at,
            self.published_at,
            self.alarm_id,
            self.description,
        ]
        placeholders = [f"${i + 1}" for i in range(len(columns))]

        sql = f"""
                       INSERT INTO druid_interactions.alarms
                       ({", ".join(columns)})
                       VALUES ({", ".join(placeholders)})
                       ON CONFLICT (alarm_id, device_id, alarm_type) DO UPDATE SET
                           severity = EXCLUDED.severity,
                           message = EXCLUDED.message,
                           updated_at = EXCLUDED.updated_at,
                           cleared = EXCLUDED.cleared,
                           description = EXCLUDED.description,
                           event_type = EXCLUDED.event_type,
                           obj_id = EXCLUDED.obj_id,
                           component = EXCLUDED.component,
                           qualifier = EXCLUDED.qualifier

                       RETURNING *
                   """
        return sql, values

    def sql_select(self, device_id: str | None = None, order_by: bool = False) -> str:
        base_query = f"SELECT * FROM {self.schema}.alarms"
        conditions = []

        if device_id:
            conditions.append("device_id = :device_id")

        # Include alarm_type filter if it's explicitly set (not the default)
        if hasattr(self, 'alarm_type') and self.alarm_type and self.alarm_type != AlarmType.WATCHDOG_DEBUG:
            conditions.append("alarm_type = :alarm_type")

        if conditions:
            base_query += " WHERE " + " AND ".join(conditions)

        # Add ordering only if requested
        if order_by:
            base_query += " ORDER BY created_at DESC"

        return base_query

    def sql_select_latest_watchdog_alert(self, device_id: str) -> str:
        """Get the latest watchdog alert alarm for a specific device."""
        return (
            f"SELECT * FROM {self.schema}.alarms "
            "WHERE device_id = :device_id "
            "AND alarm_type = :alarm_type "
            "ORDER BY created_at DESC LIMIT 1"
        )

    @classmethod
    def sql_bulk_insert(cls, entries: list) -> str:
        """Generate bulk insert SQL for multiple alarms."""
        columns = [
            'device_id', 'alarm_type', 'created_at', 'component',
            'event_type', 'obj_id', 'severity', 'message', 'cleared',
            'alarm_id', 'description', 'event_id'
        ]

        # Calculate total parameters needed
        params_per_row = len(columns)
        total_rows = len(entries)

        # Generate ($1, $2, $3), ($4, $5, $6), etc.
        value_groups = []
        for i in range(total_rows):
            start_idx = i * params_per_row + 1
            group = [f"${j}" for j in range(start_idx, start_idx + params_per_row)]
            value_groups.append(f"({', '.join(group)})")

        # Create the upsert query with conflict handling
        return f"""
            INSERT INTO {cls.schema}.alarms (
                {', '.join(columns)}
            ) VALUES {', '.join(value_groups)}
            ON CONFLICT (device_id, alarm_type, alarm_id)
            WHERE alarm_id IS NOT NULL
            DO UPDATE SET
                component = EXCLUDED.component,
                event_type = EXCLUDED.event_type,
                severity = EXCLUDED.severity,
                message = EXCLUDED.message,
                cleared = EXCLUDED.cleared,
                description = EXCLUDED.description,
                updated_at = EXCLUDED.updated_at
        """
