import dataclasses
import datetime
import functools

from druid_interactions.types import Role
from druid_interactions.types import SQLTable
from pydantic import BaseModel
from pydantic import Field


class InstanceData(BaseModel):
    name: str = Field(description="Name of the instance", examples=["dauk-mrl-green-druid"])
    host: str = Field(description="DNS host or IP", examples=["*************"])
    port: int = Field(description="Network port of API", examples=[80])
    role: Role | None = Field(description="Instance role", examples=[Role.NHE])
    secret_id: str = Field(description="ID of instance secrets")
    status: str = Field(description="Instance status", examples=["UNKNOWN"])
    created_at: datetime.datetime | None = None


@dataclasses.dataclass(eq=True)
class Instance(SQLTable):
    name: str
    host: str
    port: int = 443
    role: Role = Role.NONE
    secret_id: str | None = None
    status: str = "UNKNOWN"
    created_at: datetime.datetime = dataclasses.field(
        default_factory=functools.partial(datetime.datetime.now, tz=datetime.timezone.utc)
    )

    @property
    def url(self):
        return f"https://{self.host}:{self.port}/api"

    def sql_delete(self, name: str) -> str:
        return f"DELETE FROM {self.schema}.instances WHERE name = :name RETURNING *"

    def sql_insert(self) -> str:
        return f"INSERT INTO {self.schema}.instances (:values__names) VALUES :values"

    def sql_select(self, name: str = None) -> str:
        if name:
            return f"SELECT * FROM {self.schema}.instances WHERE name = :name"
        else:
            return f"SELECT * FROM {self.schema}.instances"

    def sql_update(self, name: str) -> str:
        # update all columns except created_at and name
        columns_to_update = {
            attr: f":{attr}" for attr in vars(self) if attr not in ["created_at", "name"]
        }
        set_clause = ", ".join(f"{col} = {val}" for col, val in columns_to_update.items())
        return f"UPDATE {self.schema}.instances SET {set_clause} WHERE name = :name"
