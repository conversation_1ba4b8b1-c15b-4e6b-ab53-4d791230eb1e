import dataclasses
import datetime
import functools

from da_common.models import Status


@dataclasses.dataclass
class ReportData:
    device_id: str
    status: Status = Status.UNKNOWN
    aspects: dict = dataclasses.field(default_factory=dict)
    details: dict = dataclasses.field(default_factory=dict)
    networks: list = dataclasses.field(default_factory=list)
    segw: dict = dataclasses.field(default_factory=dict)
    sessions: list = dataclasses.field(default_factory=list)
    enodebs: list = dataclasses.field(default_factory=list)
    created_at: datetime.datetime = dataclasses.field(
        default_factory=functools.partial(datetime.datetime.now, tz=datetime.timezone.utc)
    )
