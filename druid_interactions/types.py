import dataclasses
import enum
import functools
import json

import buildpg


class AlarmType(enum.StrEnum):
    INSTANCE_ALARM = "instance alarm"
    OFFLINE_ALERT = "offline alert"
    LICENCE_EXPIRY = "licence expiry"
    WATCHDOG_DEBUG = "watchdog debug"


class LicenceExpiryType(enum.StrEnum):
    LICENCE_EXPIRED = "Licence Expired"
    LICENCE_EXPIRING = "Licence Expiring"


class Role(enum.StrEnum):
    NONE = "Unknown"
    SGW = "Druid Security Gateway"
    NHE = "Druid Neutral Host Entity"
    CN = "Druid Core Network"


class Severity(enum.StrEnum):
    NONE = "none"
    WARNING = "warning"
    MINOR = "minor"
    MAJOR = "major"
    CRITICAL = "critical"


class SQLTable:
    schema = "druid_interactions"

    @functools.cached_property
    def _values(self) -> dict:
        "Return a dictionary with buildpg values"
        transforms = {
            dict: json.dumps,
        }

        def null(arg):
            return arg

        try:
            data = {
                k: transforms.get(type(v), null)(getattr(v, "value", v))
                for k, v in dataclasses.asdict(self).items()
            }
            return dict(data, values=buildpg.Values(**data))
        except TypeError:
            return dict(vars(self), values=buildpg.Values(**vars(self)))

    def insert_values(self) -> dict:
        """Return values to use for INSERT operations, using _values_for_insert if available."""
        if hasattr(self, "_values_for_insert"):
            return self._values_for_insert
        return self._values
