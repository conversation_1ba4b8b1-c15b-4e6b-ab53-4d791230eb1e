import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class PLMNQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "raemis_id": int,
            "long_network_name_source": lookup.get("long_network_name_source", {}).get,
            "short_network_name_source": lookup.get("short_network_name_source", {}).get,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "plmn", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SGwQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "node_id": int,
            "raemis_id": int,
            "admin_state": lookup.get("admin_state", {}).get,
            "oper_state": lookup.get("oper_state", {}).get,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "sgw", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SGwSessionQuery(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "sgw_session", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SessionsReport(Report):
    def __init__(self):
        self.queries = {
            typ: typ()
            for typ in (
                PLMNQuery,
                SGwQuery,
                SGwSessionQuery,
            )
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> list:
        sgws = {i.get("id"): i for i in data.get(SGwQuery, [])}
        rv = [dict(i, sgw=sgws.get(i.get("sgw_id"))) for i in data.get(SGwSessionQuery, [])]
        return rv
