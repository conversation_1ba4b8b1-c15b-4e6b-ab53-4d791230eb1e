from collections import Counter

import httpx
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class ENodeBCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/enodeb", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPRouteCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/ip_route", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecSecureAssociationCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/ipsec_secure_association", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class NetDeviceCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/net_device", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class PLMNCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/plmn", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class RadioZoneCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/radio_zone", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SGwCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/sgw", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SGwSessionCount(Query):
    async def get_object(
        self, client: httpx.AsyncClient, path: str = "count/sgw_session", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class S1ServerEnbCount(Query):
    async def get_object(
        self,
        client: httpx.AsyncClient,
        path: str = "count/s1_server_enb?oper_state=2",
        **kwargs,
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class UsersCount(Query):
    async def get_object(self, client: httpx.AsyncClient, **kwargs) -> httpx.Response:
        # We have different API endpoints for
        # Role.SEGW = count/s1ap_client_context
        # and Role.CN = count/subscriber
        # We need to handle both cases here
        path: str = "count/subscriber"
        response = await super().get_object(client, path, **kwargs)
        if response.status_code != 200:
            path: str = "count/s1ap_client_context"
            response = await super().get_object(client, path, **kwargs)
        return response


class AspectsReport(Report):
    def __init__(self):
        self.queries = {
            ENodeBCount: ENodeBCount(),
            FeaturesQuery: FeaturesQuery(),
            IPRouteCount: IPRouteCount(),
            IPSecSecureAssociationCount: IPSecSecureAssociationCount(),
            NetDeviceCount: NetDeviceCount(),
            PLMNCount: PLMNCount(),
            RadioZoneCount: RadioZoneCount(),
            SGwCount: SGwCount(),
            SGwSessionCount: SGwSessionCount(),
            S1ServerEnbCount: S1ServerEnbCount(),
            UsersCount: UsersCount(),
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> Counter:
        features = next(iter(data.pop(FeaturesQuery)), [])
        rv = {
            AspectsReport.camel_to_snake_case(k.__name__): next(iter(v), 0)
            for k, v in data.items()
        }
        rv["enable_5g"] = bool(features.get("enable_5g"))
        rv["enable_5g_nsa"] = bool(features.get("enable_5g_nsa"))
        return rv
