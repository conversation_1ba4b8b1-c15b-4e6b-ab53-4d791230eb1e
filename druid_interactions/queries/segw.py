import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class IPSecChildConfigQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "name": str,
            "jitter": int,
            "lifetime": int,
            "rekeytime": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_child_config", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecChildConfigProposalQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "child_cfg": int,
            "prio": int,
            "prop": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_child_config_proposal", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecCertificateQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        transforms = {
            "filename": str,
            "issuer": str,
            "subject": str,
            "start_date": IPSecCertificateQuery.utc_from_isoformat,
            "expiry_date": IPSecCertificateQuery.utc_from_isoformat,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_certificate", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecIKEConfigProposalQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "ike_cfg": int,
            "prio": int,
            "prop": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_ike_config_proposal", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecPeerConfigQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "name": str,
            "type": lookup.get("type", {}).get,
            "pool": str,
            "jitter": int,
            "rekeytime": int,
            "ike_version": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_peer_config", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecPrivateKeyQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        _lookup = Query.resolver(schema)
        transforms = {
            "type": int,
            "filename": str,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_private_key", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecSecureAssociationQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "oper_state": lookup.get("oper_state", {}).get,
            "local_addr": str,
            "local_id": str,
            "name": str,
            "remote_addr": str,
            "remote_id": str,
            "direction": lookup.get("direction", {}).get,
            "integrity_algorithm": str,
            "encryption_algorithm": str,
            "sa_bytes_in": int,
            "sa_bytes_out": int,
            "sa_packets_in": int,
            "sa_packets_out": int,
            "creation_time": IPSecSecureAssociationQuery.utc_from_isoformat,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_secure_association", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class IPSecProposalQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "proposal": str,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipsec_proposal", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class S1ClientQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "admin_state": lookup.get("admin_state", {}).get,
            "oper_state": lookup.get("oper_state", {}).get,
            "name": str,
            "enb_name": str,
            "s1_client_type": lookup.get("s1_client_type", {}).get,
            "plmn_id": str,
            "cell_identity": str,
            "sa_bytes_in": int,
            "sa_bytes_out": int,
            "max_uplink_bandwidth": int,
            "max_downlink_bandwidth": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "s1_client", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SeGWReport(Report):
    def __init__(self):
        self.queries = {
            typ: typ()
            for typ in (
                IPSecChildConfigQuery,
                IPSecChildConfigProposalQuery,
                IPSecCertificateQuery,
                IPSecIKEConfigProposalQuery,
                IPSecPeerConfigQuery,
                IPSecPrivateKeyQuery,
                IPSecSecureAssociationQuery,
                IPSecProposalQuery,
                S1ClientQuery,
            )
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> dict:
        rv = {
            Report.camel_to_snake_case(t.__name__).removesuffix("_query"): v
            for t, v in data.items()
        }
        return rv
