from collections.abc import Generator

import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report
from inventory_discovery.models.published_types import ManifestAction
from inventory_discovery.models.published_types import ManifestType
from inventory_discovery.models.published_types import VersionManifestMessage


class SystemQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        data = data[0] if data and isinstance(data, list) else data
        schema = {
            item["name"]: {
                option["value"]: option["description"].partition("-")[0].strip()
                for option in item.get("choices", [])
            }
            for item in schema.get("attributes", [])
        }
        transforms = {
            "id": int,
            "admin_state": schema.get("admin_state", {}).get,
            "current_time": lambda v: (
                SystemQuery.utc_from_isoformat(v).isoformat().replace("+00:00", "Z")
                if SystemQuery.utc_from_isoformat(v)
                else None
            ),
            "license_id": str,
            "oper_state": schema.get("oper_state", {}).get,
            "product_id": str,
            "restart_required": str,
            "service_state": schema.get("service_state", {}).get,
            "software_version": str,
            "system_id": str,
        }
        rv = {k: transforms[k](v) or str(v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "raemis", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class SystemReport(Report):
    def __init__(self):
        self.queries = {SystemQuery: SystemQuery()}

    @staticmethod
    def build_report(data: dict[type, list]) -> dict:
        rv = {k.__name__: v for k, v in data.items()}
        return rv

    @staticmethod
    def items(report: list) -> dict[str, dict]:
        data = {
            "software_version": value
            for record in report.get("SystemQuery", [])
            if (value := record.get("software_version"))
        }
        try:
            return {data["software_version"]: data}
        except KeyError:
            return {}

    @staticmethod
    def events(item: dict, manifest_id="") -> Generator[dict]:
        parts = [i.strip(" .") for i in item.get("software_version", "").partition(",")]
        yield dict(
            event_type=VersionManifestMessage,
            manifest_id=manifest_id,
            manifest_type=ManifestType.MANAGER_MANIFEST,
            action=ManifestAction.UPDATE,
            manifest=dict(version=parts[0], release=None),
        )
