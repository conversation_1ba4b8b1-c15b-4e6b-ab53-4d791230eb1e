import datetime

import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report
from druid_interactions.queries.system import SystemQuery
from druid_interactions.types import LicenceExpiryType
from druid_interactions.types import Severity


class FeaturesQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "license_id": str,
            "license_status": str,
            "binding_date": FeaturesQuery.utc_from_isoformat,
            "expiry_date": FeaturesQuery.utc_from_isoformat,
            "issue_date": FeaturesQuery.utc_from_isoformat,
            "oper_state": lookup.get("oper_state", {}).get,
            "product_id": str,
            "supported_until": FeaturesQuery.utc_from_isoformat,
            "system_id": str,
            "max_cells": str,
            "max_enbs": str,
            "max_nbr_of_subs": str,
            "max_pdns": str,
            "max_s1_clients": str,
            "enbgw_max_active_subs": str,
            "enbgw_max_enbs": str,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "features", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class LicenceReport(Report):
    def __init__(self):
        self.queries = {
            FeaturesQuery: FeaturesQuery(),
            SystemQuery: SystemQuery(),
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> dict:
        return {
            "features": data[FeaturesQuery] and data[FeaturesQuery][0],
            "system": data[SystemQuery] and data[SystemQuery][0],
        }

    @staticmethod
    def _get_days_left(expiry_date: datetime.datetime) -> int:
        """
        Calculate the number of days left until expiry.

        Args:
            expiry_date: The date when the license expires.

        Returns:
            Number of days left until expiry (negative if already expired).
        """
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        return (expiry_date - now).days

    @staticmethod
    def _get_severity_for_days(days_left: int) -> Severity:
        """
        Determine the severity level based on days left until expiry.

        Args:
            days_left: Number of days left until expiry.

        Returns:
            Appropriate severity level.
        """
        if days_left < 0:
            return Severity.CRITICAL
        elif days_left <= 5:
            return Severity.MAJOR
        elif days_left <= 15:
            return Severity.MINOR
        elif days_left <= 30:
            return Severity.WARNING
        else:
            return Severity.NONE

    @staticmethod
    def _get_description(days_left: int) -> str:
        """
        Generate a human-readable description based on days left.

        Args:
            days_left: Number of days left until expiry.

        Returns:
            Human-readable description of the expiry status.
        """
        if days_left < 0:
            return "Licence has expired"
        elif days_left == 0:
            return "Licence will expire today"
        elif days_left == 1:
            return "Licence will expire tomorrow"
        else:
            return f"Licence will expire in {days_left} days"

    @staticmethod
    def _get_expiry_type(days_left: int) -> LicenceExpiryType:
        """
        Determine the license expiry type based on days left.

        Args:
            days_left: Number of days left until expiry.

        Returns:
            License expiry type or None if not expiring soon.
        """
        if days_left < 0:
            return LicenceExpiryType.LICENCE_EXPIRED
        elif days_left <= 30:  # Only set expiring if within 30 days
            return LicenceExpiryType.LICENCE_EXPIRING
        return None

    @staticmethod
    def check_expiry(data: dict) -> dict:
        """
        Check for license expiry with different severity levels based on days remaining.

        Severity levels:
        - WARNING: expiring in 30 days
        - MINOR: expiring in 15 days
        - MAJOR: expiring in 5 days
        - CRITICAL: already expired

        Args:
            data: Dictionary containing license data with an 'expiry_date' field.

        Returns:
            Dictionary with severity, description, and licence_expiry_type.
        """

        # Handle missing expiry date
        if not (expiry_date := data.get("expiry_date")):
            return {
                "severity": Severity.NONE,
                "description": "no expiry date found",
                "licence_expiry_type": None,
            }

        # Calculate days left and determine severity, description, and type
        days_left = LicenceReport._get_days_left(expiry_date)
        severity = LicenceReport._get_severity_for_days(days_left)

        # Only set description and type if there's a severity
        if severity != Severity.NONE:
            description = LicenceReport._get_description(days_left)
            licence_expiry_type = LicenceReport._get_expiry_type(days_left)
        else:
            description = ""
            licence_expiry_type = None

        return {
            "severity": severity,
            "description": description,
            "licence_expiry_type": licence_expiry_type,
        }
