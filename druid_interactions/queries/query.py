import datetime

import httpx
from druid_interactions.models.instance import Instance


class Query:
    registry: dict = {}

    @staticmethod
    def utc_from_isoformat(text: str) -> datetime.datetime | None:
        try:
            # Eg: expiry_date "Dec 06 17:30:17 2025"
            return datetime.datetime.strptime(text, "%b %d %H:%M:%S %Y").replace(
                tzinfo=datetime.timezone.utc
            )
        except ValueError:
            pass

        if "-" not in text:
            # Repair misformatted timestamps, eg: "2024:05:10 09:25:02.692"
            text = "T".join(
                i.replace(":", "-") if not n else i for n, i in enumerate(text.split(" "))
            )
        try:
            return datetime.datetime.fromisoformat(text).replace(tzinfo=datetime.timezone.utc)
        except ValueError:
            return None

    @staticmethod
    def resolver(schema: dict = {}) -> dict:
        "Create a mapping from an object schema to look up enumeration values"
        return {
            item["name"]: {
                option["value"]: option["description"].partition("-")[0].strip()
                for option in item.get("choices", [])
            }
            for item in schema.get("attributes", [])
        }

    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        return data

    @staticmethod
    def client(
        instance: Instance,
        clients: dict[str, httpx.AsyncClient],
        verify: bool = True,
        **kwargs,
    ) -> httpx.AsyncClient:
        if instance.name not in clients:
            auth = httpx.BasicAuth(**kwargs)
            clients[instance.name] = httpx.AsyncClient(
                auth=auth, base_url=instance.url, verify=verify
            )

        return clients[instance.name]

    async def get_object(
        self, client: httpx.AsyncClient, path: str, **kwargs
    ) -> httpx.Response:
        response = await client.get(path, **kwargs)
        if "/" not in path and response.url not in self.registry:
            # Get Raemis schema for object type
            reply = await client.get(f"/schema/{path}")
            if reply.status_code == 200:
                self.registry[response.url] = reply.json()
        return response
