import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class ENodeBQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "name": str,
            "admin_state": lookup.get("admin_state", {}).get,
            "oper_state": lookup.get("oper_state", {}).get,
            "last_inform_time": Query.utc_from_isoformat,
            "plmn_id": str,
            "identity": int,
            "sctp_address": str,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "enodeb", **kwargs
    ) -> httpx.Response:
        response = await super().get_object(client, path, **kwargs)
        if not response.json():
            response = await super().get_object(client, "s1_server_enb", **kwargs)
        return response


class ENodeBTrxQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "name": str,
            "admin_state": lookup.get("admin_state", {}).get,
            "oper_state": lookup.get("oper_state", {}).get,
            "last_inform_time": Query.utc_from_isoformat,
            "cell_id": int,
            "enb_id": int,
            "tac": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "enodeb_trx", **kwargs
    ) -> httpx.Response:
        response = await super().get_object(client, path, **kwargs)
        if not response.json():
            response = await super().get_object(client, "s1_server_enodeb_trx", **kwargs)
        return response


class ENodeBReport(Report):
    def __init__(self):
        self.queries = {
            ENodeBQuery: ENodeBQuery(),
            ENodeBTrxQuery: ENodeBTrxQuery(),
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> dict:
        return {k.__name__: v for k, v in data.items()}

    @staticmethod
    def items(report: dict) -> dict[int, dict]:
        "Join each ENodeBTrx with its ENodeB and map to cell_id"

        enodebs = {
            enodeb["id"]: enodeb for enodeb in report.get("ENodeBQuery", []) if enodeb.get("id")
        }
        return {
            enodeb_trx.get("cell_id"): dict(
                enodeb_trx, enodeb=enodebs.get(enodeb_trx.get("enb_id"))
            )
            for enodeb_trx in report.get("ENodeBTrxQuery", [])
            if enodeb_trx.get("enb_id") in enodebs.keys()
        }
