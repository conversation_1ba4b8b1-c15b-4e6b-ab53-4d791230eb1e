import re
import warnings

from druid_interactions.models.instance import Instance


class Report:
    @staticmethod
    def camel_to_snake_case(text: str):
        def underscore(m):
            return f"{m['tail']}_{m['head']}"

        regex = re.compile("(?P<tail>[a-z])(?P<head>[A-Z])")
        text = text.replace("Count", "_count")
        text = regex.sub(underscore, text)
        return text.lower()

    @staticmethod
    def build_report(data: dict[type, list]) -> dict | list:
        raise NotImplementedError

    async def get_report_data(
        self, instance: Instance, clients: dict | None = None, creds: dict = {}, **kwargs
    ) -> dict:
        clients = clients or dict()
        username = creds.get("username", "")
        password = creds.get("password", "")

        results = {}
        for query_type, query in self.queries.items():
            client = query.client(
                instance,
                clients,
                username=username,
                password=password,
                **kwargs,
            )
            response = await query.get_object(client)
            schema = query.registry.get(response.url, {})
            data = response.json()
            if isinstance(data, dict):
                results[query_type] = [query.normalize_data(data, schema=schema)]
            elif isinstance(data, (list, tuple)):
                results[query_type] = [
                    query.normalize_data(item, schema=schema) for item in data
                ]
            else:
                warnings.warn(f"Unable to process {data}")

        return results
