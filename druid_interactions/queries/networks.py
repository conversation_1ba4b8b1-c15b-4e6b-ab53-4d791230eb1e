import ipaddress

import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class IPRouteQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        transforms = {
            "dnm_managed": bool,
            "id": int,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ip_route", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class NetDeviceQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "admin_state": lookup.get("admin_state", {}).get,
            "device": str,
            "parent_device": str,
            "bootproto": str,
            "ip": str,
            "netmask": str,
            "ipv6": str,
            "nat_enabled": bool,
            "owner": str,
            "device_type": lookup.get("device_type", {}).get,
            "oper_state": lookup.get("oper_state", {}).get,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}

        # add cidr to rv
        rv["cidr"] = (
            ipaddress.IPv4Network(f"0.0.0.0/{rv['netmask']}").prefixlen
            if rv["netmask"]
            else None
        )
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "net_device", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class PdnQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        _lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "apn": str,
            "primary_dns": str,
            "secondary_dns": str,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "pdn", **kwargs
    ) -> httpx.Response:
        data = await super().get_object(client, path, **kwargs)
        return data


class GroupQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "name": str,
            "description": str,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "group", **kwargs
    ) -> httpx.Response:
        data = await super().get_object(client, path, **kwargs)
        return data


class IPV4PoolQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "name": str,
            "first_ip": str,
            "last_ip": str,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "ipv4_pool", **kwargs
    ) -> httpx.Response:
        data = await super().get_object(client, path, **kwargs)
        return data


class NetworkSliceQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "name": str,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "network_slice", **kwargs
    ) -> httpx.Response:
        data = await super().get_object(client, path, **kwargs)
        return data


class SubscriptionProfileQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "name": str,
            "apn": str,
            "network_slice_id": int,
            "apply_to_all_subs": int,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "subscription_profile", **kwargs
    ) -> httpx.Response:
        data = await super().get_object(client, path, **kwargs)
        return data


class MGWEndpointQuery(Query):
    @staticmethod
    def normalize_data(data: dict | list[dict], schema: dict = {}) -> dict:
        transforms = {
            "id": int,
            "net_device": str,
            "subnet_routing_enabled": bool,
        }
        rv = {k: transforms.get(k, str)(v) for k, v in data.items()}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "mgw_endpoint", **kwargs
    ) -> httpx.Response:
        data = await super().get_object(client, path, **kwargs)
        return data


class NetworksReport(Report):
    def __init__(self):
        self.queries = {
            typ: typ()
            for typ in (
                NetDeviceQuery,
                IPRouteQuery,
                PdnQuery,
                GroupQuery,
                IPV4PoolQuery,
                MGWEndpointQuery,
                NetworkSliceQuery,
                SubscriptionProfileQuery,
                # IPSecSecureAssociationQuery,
            )
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> dict:
        return {
            "ip_route": data.get(IPRouteQuery, []),
            "pdn": data.get(PdnQuery, []),
            "group": data.get(GroupQuery, []),
            "ipv4_pool": data.get(IPV4PoolQuery, []),
            "mgw_endpoint": data.get(MGWEndpointQuery, []),
            "net_device": data.get(NetDeviceQuery, []),
            "network_slice": data.get(NetworkSliceQuery, []),
            "subscription_profile": data.get(SubscriptionProfileQuery, []),
        }
