from collections import defaultdict

import httpx
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class ENodeBTrxQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "admin_state": lookup.get("admin_state", {}).get,
            "name": str,
            "location": str,
            "oper_state": lookup.get("oper_state", {}).get,
            "downlink_bandwidth": lookup.get("downlink_bandwidth", {}).get,
            "uplink_bandwidth": lookup.get("uplink_bandwidth", {}).get,
            "cell_id": int,
            "enb_id": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "enodeb_trx", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class ENodeBQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        lookup = Query.resolver(schema)
        transforms = {
            "id": int,
            "admin_state": lookup.get("admin_state", {}).get,
            "device_type": lookup.get("device_type", {}).get,
            "nat_enabled": bool,
            "oper_state": lookup.get("oper_state", {}).get,
            "PKI_Enable": bool,
            "PerfMgmt_Config_Enable": bool,
            "en_dc_enabled": bool,
            "leds_enabled": bool,
            "enb_type": lookup.get("enb_type", {}).get,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "enodeb", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class EntitiesReport(Report):
    def __init__(self):
        self.queries = {
            typ: typ()
            for typ in (
                ENodeBQuery,
                ENodeBTrxQuery,
            )
        }

    @staticmethod
    def build_report(data: dict[type, list]) -> list:
        """
        Join each ENodeBTrx with its ENodeB
        """

        end_id_to_enb_trx = defaultdict(list)
        for enb_trx in data.get(ENodeBTrxQuery, []):
            end_id = enb_trx.get("enb_id")
            end_id_to_enb_trx[end_id].append(enb_trx)

        enbs = []
        for enb in data.get(ENodeBQuery, []):
            enb["enb_trx"] = end_id_to_enb_trx.get(enb.get("id"), [])
            enbs.append(enb)
        return enbs
