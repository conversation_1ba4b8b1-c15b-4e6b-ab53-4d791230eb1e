import logging

import httpx
from druid_interactions.models.instance import Instance
from druid_interactions.queries.features import FeaturesQuery
from druid_interactions.queries.features import LicenceReport
from druid_interactions.queries.query import Query
from druid_interactions.queries.report import Report


class AlarmsQuery(Query):
    @staticmethod
    def normalize_data(data: dict, schema: dict = {}) -> dict:
        _ = Query.resolver(schema)
        transforms = {
            "obj_id": int,
            "obj_class": str,
            "alarm_identifier": str,
            "event_type": str,
            "severity": str,
            "specific_problem": str,
            "probable_cause": str,
            "add_text": str,
            "start_time": AlarmsQuery.utc_from_isoformat,
            "acknowledged": bool,
            "id": int,
        }
        rv = {k: transforms[k](v) for k, v in data.items() if k in transforms}
        return rv

    async def get_object(
        self, client: httpx.AsyncClient, path: str = "alarm", **kwargs
    ) -> httpx.Response:
        return await super().get_object(client, path, **kwargs)


class StatusReport(Report):
    def __init__(self):
        self.queries = {
            AlarmsQuery: AlarmsQuery(),
            FeaturesQuery: FeaturesQuery(),
        }

    @staticmethod
    def check_expiry(data: dict):
        return LicenceReport.check_expiry(data)

    @staticmethod
    def build_report(data: dict[type, list]) -> dict:
        data[FeaturesQuery] = data[FeaturesQuery] and data[FeaturesQuery][0]
        return data

    async def get_report_data(
        self, instance: Instance, clients: dict = {}, creds: dict = {}, **kwargs
    ) -> dict:
        logger = logging.getLogger("druid_interactions.alarms")
        results = await super().get_report_data(instance, clients, creds, **kwargs)

        username = creds.get("username", "")
        password = creds.get("password", "")
        for alarm_result in results.get(AlarmsQuery, []):
            try:
                client = self.queries[AlarmsQuery].client(
                    instance,
                    clients,
                    username=username,
                    password=password,
                    verify=False,
                )
                uri = f"{alarm_result['obj_class']}?id={alarm_result['obj_id']}"
                response = await client.get(uri)
                alarm_result["extra_info"] = next(iter(response.json()), {})
            except KeyError:
                logger.warning(f"No further info on alarm {alarm_result}")
            except Exception as e:
                logger.warning(f"Error while enriching alarm: {e!s}")
        return results
