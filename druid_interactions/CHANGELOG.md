# Change History

## 0.1.0

- [NMS-1290](https://denseair.atlassian.net/browse/NMS-1290): Establish microservice.

  0.2.0

---

- [NMS-1614](https://denseair.atlassian.net/browse/NMS-1614): Minimum Viable Product.

  0.3.0

---

- [NMS-1634](https://denseair.atlassian.net/browse/NMS-1634): Add status API.

  0.4.0

---

- [NMS-1637](https://denseair.atlassian.net/browse/NMS-1637): Add reports API.

  0.5.0

---

- [NMS-1635](https://denseair.atlassian.net/browse/NMS-1635): Add Watchdog for licence expiry.

  0.6.0

---

- [NMS-1729](https://denseair.atlassian.net/browse/NMS-1729): Pass through Druid alarms.

  0.7.0

---

- [NMS-1680](https://denseair.atlassian.net/browse/NMS-1680): Unify API models.

  0.8.0

---

- [NMS-1745](https://denseair.atlassian.net/browse/NMS-1745): Fix alarm url.

  0.9.0

---

- [NMS-1616](https://denseair.atlassian.net/browse/NMS-1616): Consolidate report API.

  0.9.1

---

- [NMS-1797](https://denseair.atlassian.net/browse/NMS-1797): Better population of event type.

  0.10.0

---

- [NMS-1638](https://denseair.atlassian.net/browse/NMS-1638): Add entity report endpoint.

  0.11.0

---

- [NMS-1795](https://denseair.atlassian.net/browse/NMS-1795): Publish status changes.

  0.12.0

---

- [NMS-1753](https://denseair.atlassian.net/browse/NMS-1753): Reset Raemis via API.

  1.0.0

---

- [NMS-1897](https://denseair.atlassian.net/browse/NMS-1897): Fixes for production.

  1.1.0

---

- [NMS-1926](https://denseair.atlassian.net/browse/NMS-1926): Fix exception logging.

  1.3.0

---

- [NMS-1912](https://denseair.atlassian.net/browse/NMS-1912): Implement SeGW queries and report.

  1.3.1

---

- [NMS-1927](https://denseair.atlassian.net/browse/NMS-1927): Preserve alarm entries in DB.

  1.3.2

---

- [NMS-2152](https://denseair.atlassian.net/browse/NMS-2152): Use alarm.message as specificProblem text.

  1.4.0

---

- [NMS-2151](https://denseair.atlassian.net/browse/NMS-2151): Monitor task surveys eNodeBs.

  1.4.1

---

- [NMS-2155](https://denseair.atlassian.net/browse/NMS-2155): Query enodebs by Cell id.

  1.4.2

---

- [NMS-2156](https://denseair.atlassian.net/browse/NMS-2156): Populate alarms with Cell id.

  1.4.3

---

- [NMS-2013](https://denseair.atlassian.net/browse/NMS-2013): Publish system version changes.

  1.5.0

---

- [NMS-2050](https://denseair.atlassian.net/browse/NMS-2050): Include eNodeBs in report endpoint.

  1.5.1

---

- [NMS-2169](https://denseair.atlassian.net/browse/NMS-2169): Update watchdog alarms cache

  1.5.2

---

- [NMS-2036](https://denseair.atlassian.net/browse/NMS-2036): Exclude Druid ENodeBs Alarm while computing Druid Instance Status

  1.6.0

---

- [NMS-2287](https://denseair.atlassian.net/browse/NMS-2087): Capture Total Licence and Used Licence for each Druid Instance

  1.6.1

---

- [NMS-2460](https://denseair.atlassian.net/browse/NMS-2460): Enrich Networks Data

  1.7.0

---

- [NMS-2553](https://denseair.atlassian.net/browse/NMS-2553): Publish Druid License Metrics

  1.8.0

---

- [NMS-2648](https://denseair.atlassian.net/browse/NMS-2648): Fix Druid Alarms Reconcilation

  1.8.1

---

- [NMS-2641](https://denseair.atlassian.net/browse/NMS-2641): Add memory usage to health check response

  1.8.2

---

- [NMS-2701](https://denseair.atlassian.net/browse/NMS-2641): Fix None alarm_id in Offline Alert Alarm

  1.8.3

---

- [NMS-2718](https://denseair.atlassian.net/browse/NMS-2718): Fix publishing duplicate alarm updats

  1.9.0

---

- [NMS-2700](https://denseair.atlassian.net/browse/NMS-2700): Enriched Licence Expiry Alarms with Days Left

  1.9.1

- Fix sending multplie alarms for license expiry even when no change
