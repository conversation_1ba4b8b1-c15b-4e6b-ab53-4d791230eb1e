from druid_interactions.models.alarm import AlarmEntry
from druid_interactions.pool import PoolFactory


async def get_alarms(pool: PoolFactory, instance_name: str):
    rows = []
    query = AlarmEntry(device_id=instance_name)
    async with pool.acquire() as connection:
        async with connection.transaction():
            sql, args = pool.render(
                query.sql_select(device_id=query.device_id), **query._values
            )
            rows = await connection.fetch(sql, *args)

    return rows
