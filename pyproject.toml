# For possible options see https://peps.python.org/pep-0621/

[project]
name = "druid_interactions"
version = "1.9.1"
description = "Druid interactions"
readme = "README.md"
requires-python = ">=3.11"
license = { text = "Proprietary" }
keywords = ["NMS", "Druid"]
authors = [
    { name = "<PERSON>" }
]
classifiers = [
    "Programming Language :: Python"
]
dependencies = [
    "aiosqlite",
    "asyncpg >= 0.29.0",
    "buildpg >= 0.4",
    "da_common>=1.3.8",
    "inventory_discovery >= 0.3.1",
    "metrics_collector",
    "pydantic >= 2.0", # Version is constrained by fastapi
]

[project.optional-dependencies]
audit = [
    "pip-licenses >= 3.5.5",
]
test = [
    "asgi-lifespan>=2.1.0",
    "respx >= 0.20.1",
    "sqlalchemy[asyncio] >= 2.0.0",
    "sqlmodel >= 0.0.8",
    "pytest"
]
deploy = [
    "asyncpg >= 0.27.0",
    "buildpg >= 0.4",
    "dal_pubsub",
    "fastapi >= 0.109.0",
    "httpx >= 0.23.0",
    "hypercorn >= 0.14.3",
]

[project.scripts]

[build-system]
requires = [
    "hatchling",
    "wheel"
]

build-backend = "hatchling.build"

[tool.hatch.build]
include = ["druid_interactions/*"]

[tool.ruff]
line-length = 120


[tool.black]
    line-length = 96
    target-version = ["py310"]
    include = '\.pyi?$'
    exclude = '''
    /(
        \.git
      | \.hg
      | \.mypy_cache
      | \.tox
      | \.venv
      | _build
      | buck-out
      | build
      | dist
    )/
    '''


[tool.isort]
    line_length = 96
    profile = "black"
    src_paths = ["airspan_acp_agent"]
    no_lines_before="LOCALFOLDER"
    known_first_party = ["airspan_acp_agent"]
    include_trailing_comma = true
    multi_line_output = 3
    lines_after_imports = 2
    lines_between_types = 0
    force_single_line = true
    skip_glob = [
        "airspan_acp_agent/__init__.py",
        "airspan_acp_agent/cfg/__init__.py",
        "airspan_acp_agent/models/__init__.py",
        "airspan_acp_agent/test/__init__.py",
    ]

